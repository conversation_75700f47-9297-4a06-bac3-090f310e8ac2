/**
 * WebRTC指纹防护和IP泄露防护
 * 防止通过WebRTC API获取真实IP地址和设备信息
 */

(function() {
    'use strict';
    
    // 配置选项
    const config = {
        blockWebRTC: true,           // 是否完全阻止WebRTC
        spoofLocalIPs: true,         // 是否伪装本地IP
        spoofPublicIPs: true,        // 是否伪装公网IP
        allowedIPs: ['127.0.0.1'],   // 允许的IP地址
        fakeLocalIP: '*************', // 伪装的本地IP
        fakePublicIP: '*******'      // 伪装的公网IP
    };
    
    // 生成伪造的ICE候选
    const generateFakeCandidate = function(type = 'host') {
        const fakeIP = type === 'host' ? config.fakeLocalIP : config.fakePublicIP;
        const port = Math.floor(Math.random() * 10000) + 50000;
        const foundation = Math.random().toString(36).substring(2, 10);
        
        return {
            candidate: `candidate:${foundation} 1 udp ${Math.floor(Math.random() * 1000000)} ${fakeIP} ${port} typ ${type}`,
            sdpMLineIndex: 0,
            sdpMid: 'data'
        };
    };
    
    // 保存原始方法
    const originalRTCPeerConnection = window.RTCPeerConnection;
    const originalGetUserMedia = navigator.mediaDevices ? navigator.mediaDevices.getUserMedia : null;
    const originalEnumerateDevices = navigator.mediaDevices ? navigator.mediaDevices.enumerateDevices : null;
    
    if (config.blockWebRTC && originalRTCPeerConnection) {
        // 完全阻止WebRTC
        window.RTCPeerConnection = function() {
            throw new Error('WebRTC is not supported');
        };
        
        // 阻止其他WebRTC相关API
        window.webkitRTCPeerConnection = undefined;
        window.mozRTCPeerConnection = undefined;
        window.RTCSessionDescription = undefined;
        window.RTCIceCandidate = undefined;
        
    } else if (originalRTCPeerConnection) {
        // 伪装WebRTC
        window.RTCPeerConnection = function(config, constraints) {
            const pc = new originalRTCPeerConnection(config, constraints);
            
            // 劫持createOffer方法
            const originalCreateOffer = pc.createOffer;
            pc.createOffer = function(options) {
                return originalCreateOffer.call(this, options).then(offer => {
                    // 修改SDP以移除真实IP信息
                    if (offer.sdp) {
                        offer.sdp = offer.sdp.replace(
                            /c=IN IP4 (\d+\.\d+\.\d+\.\d+)/g,
                            `c=IN IP4 ${config.fakeLocalIP}`
                        );
                    }
                    return offer;
                });
            };
            
            // 劫持createAnswer方法
            const originalCreateAnswer = pc.createAnswer;
            pc.createAnswer = function(options) {
                return originalCreateAnswer.call(this, options).then(answer => {
                    if (answer.sdp) {
                        answer.sdp = answer.sdp.replace(
                            /c=IN IP4 (\d+\.\d+\.\d+\.\d+)/g,
                            `c=IN IP4 ${config.fakeLocalIP}`
                        );
                    }
                    return answer;
                });
            };
            
            // 劫持onicecandidate事件
            const originalOnIceCandidate = pc.onicecandidate;
            Object.defineProperty(pc, 'onicecandidate', {
                set: function(handler) {
                    originalOnIceCandidate = handler;
                    pc.addEventListener('icecandidate', function(event) {
                        if (event.candidate && event.candidate.candidate) {
                            // 检查是否包含需要隐藏的IP
                            const candidate = event.candidate.candidate;
                            const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
                            
                            if (ipMatch && !config.allowedIPs.includes(ipMatch[1])) {
                                // 创建伪造的候选
                                const fakeCandidate = generateFakeCandidate();
                                const fakeEvent = new RTCPeerConnectionIceEvent('icecandidate', {
                                    candidate: new RTCIceCandidate(fakeCandidate)
                                });
                                
                                if (originalOnIceCandidate) {
                                    originalOnIceCandidate.call(this, fakeEvent);
                                }
                                return;
                            }
                        }
                        
                        if (originalOnIceCandidate) {
                            originalOnIceCandidate.call(this, event);
                        }
                    });
                },
                get: function() {
                    return originalOnIceCandidate;
                }
            });
            
            return pc;
        };
        
        // 复制原始构造函数的属性
        Object.setPrototypeOf(window.RTCPeerConnection, originalRTCPeerConnection);
        window.RTCPeerConnection.prototype = originalRTCPeerConnection.prototype;
    }
    
    // 劫持getUserMedia
    if (navigator.mediaDevices && originalGetUserMedia) {
        navigator.mediaDevices.getUserMedia = function(constraints) {
            console.log('WebRTC: getUserMedia blocked/modified');
            
            // 可以选择完全阻止或返回虚假流
            return Promise.reject(new Error('Permission denied'));
        };
    }
    
    // 劫持enumerateDevices
    if (navigator.mediaDevices && originalEnumerateDevices) {
        navigator.mediaDevices.enumerateDevices = function() {
            console.log('WebRTC: enumerateDevices spoofed');
            
            // 返回伪造的设备列表
            return Promise.resolve([
                {
                    deviceId: 'default',
                    kind: 'audioinput',
                    label: 'Default - Microphone (Built-in)',
                    groupId: 'group1'
                },
                {
                    deviceId: 'default',
                    kind: 'audiooutput', 
                    label: 'Default - Speaker (Built-in)',
                    groupId: 'group1'
                },
                {
                    deviceId: 'default',
                    kind: 'videoinput',
                    label: 'Default - Camera (Built-in)',
                    groupId: 'group2'
                }
            ]);
        };
    }
    
    // 劫持其他可能泄露IP的API
    if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            // 检查是否是STUN服务器请求
            if (typeof url === 'string' && url.includes('stun:')) {
                console.log('WebRTC: STUN request blocked');
                return Promise.reject(new Error('Network error'));
            }
            
            return originalFetch.apply(this, arguments);
        };
    }
    
    // 劫持XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        if (typeof url === 'string' && url.includes('stun:')) {
            console.log('WebRTC: STUN XHR request blocked');
            throw new Error('Network error');
        }
        
        return originalXHROpen.apply(this, arguments);
    };
    
    // 防止通过WebSocket泄露IP
    if (window.WebSocket) {
        const originalWebSocket = window.WebSocket;
        window.WebSocket = function(url, protocols) {
            // 检查WebSocket URL是否可能用于IP检测
            if (typeof url === 'string' && (
                url.includes('stun:') || 
                url.includes('turn:') ||
                url.match(/ws:\/\/\d+\.\d+\.\d+\.\d+/)
            )) {
                console.log('WebRTC: Suspicious WebSocket blocked');
                throw new Error('Connection failed');
            }
            
            return new originalWebSocket(url, protocols);
        };
        
        Object.setPrototypeOf(window.WebSocket, originalWebSocket);
        window.WebSocket.prototype = originalWebSocket.prototype;
    }
    
    // 添加一些额外的防护
    Object.defineProperty(navigator, 'connection', {
        get: function() {
            return {
                effectiveType: '4g',
                type: 'wifi',
                downlink: 10,
                rtt: 50
            };
        }
    });
    
    // 隐藏真实的网络信息
    if (navigator.connection) {
        Object.defineProperty(navigator.connection, 'type', {
            get: function() { return 'wifi'; }
        });
    }
    
    console.log('✅ WebRTC Protection activated');
    
})();

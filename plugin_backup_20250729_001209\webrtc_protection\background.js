/**
 * WebRTC Protection Background Script
 * 处理网络请求拦截和配置管理
 */

// 默认配置
const defaultConfig = {
    blockWebRTC: true,
    spoofLocalIPs: true,
    spoofPublicIPs: true,
    allowedIPs: ['127.0.0.1', '::1'],
    fakeLocalIP: '*************',
    fakePublicIP: '*******',
    blockSTUN: true,
    blockTURN: true
};

// 初始化扩展
chrome.runtime.onInstalled.addListener(() => {
    console.log('WebRTC Protection extension installed');
    
    // 设置默认配置
    chrome.storage.local.set({ webrtcConfig: defaultConfig });
});

// STUN/TURN服务器列表（用于阻止）
const stunTurnServers = [
    'stun.l.google.com',
    'stun1.l.google.com',
    'stun2.l.google.com',
    'stun3.l.google.com',
    'stun4.l.google.com',
    'stun.services.mozilla.com',
    'stun.stunprotocol.org',
    'stun.ekiga.net',
    'stun.ideasip.com',
    'stun.schlund.de',
    'stun.voiparound.com',
    'stun.voipbuster.com',
    'stun.voipstunt.com',
    'stun.voxgratia.org'
];

// 拦截网络请求
chrome.webRequest.onBeforeRequest.addListener(
    function(details) {
        const url = details.url.toLowerCase();
        
        // 检查是否是STUN/TURN请求
        for (const server of stunTurnServers) {
            if (url.includes(server)) {
                console.log('Blocked STUN/TURN request:', details.url);
                return { cancel: true };
            }
        }
        
        // 检查是否是WebRTC相关的请求
        if (url.includes('stun:') || url.includes('turn:')) {
            console.log('Blocked WebRTC request:', details.url);
            return { cancel: true };
        }
        
        return {};
    },
    {
        urls: ["<all_urls>"],
        types: ["xmlhttprequest", "websocket", "other"]
    },
    ["blocking"]
);

// 修改请求头以隐藏WebRTC相关信息
chrome.webRequest.onBeforeSendHeaders.addListener(
    function(details) {
        const headers = details.requestHeaders;
        
        // 移除可能泄露WebRTC信息的头部
        const filteredHeaders = headers.filter(header => {
            const name = header.name.toLowerCase();
            return !name.includes('webrtc') && 
                   !name.includes('ice') && 
                   !name.includes('stun') &&
                   !name.includes('turn');
        });
        
        return { requestHeaders: filteredHeaders };
    },
    {
        urls: ["<all_urls>"],
        types: ["xmlhttprequest", "websocket"]
    },
    ["blocking", "requestHeaders"]
);

// 处理来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'getConfig') {
        chrome.storage.local.get(['webrtcConfig'], (result) => {
            sendResponse(result.webrtcConfig || defaultConfig);
        });
        return true; // 异步响应
    }
    
    if (request.action === 'updateConfig') {
        chrome.storage.local.set({ webrtcConfig: request.config }, () => {
            sendResponse({ success: true });
        });
        return true;
    }
    
    if (request.action === 'logBlocked') {
        console.log('WebRTC blocked:', request.details);
        sendResponse({ received: true });
    }
});

// 监听标签页更新，重新注入保护脚本
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'loading' && tab.url) {
        // 确保保护脚本在页面加载时就生效
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['content_script.js'],
            injectImmediately: true
        }).catch(err => {
            // 忽略错误（可能是特殊页面如chrome://）
            console.debug('Script injection failed:', err);
        });
    }
});

// 定期清理和更新配置
setInterval(() => {
    chrome.storage.local.get(['webrtcConfig'], (result) => {
        if (!result.webrtcConfig) {
            chrome.storage.local.set({ webrtcConfig: defaultConfig });
        }
    });
}, 60000); // 每分钟检查一次

console.log('WebRTC Protection background script loaded');

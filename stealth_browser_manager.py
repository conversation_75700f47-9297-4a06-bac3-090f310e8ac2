"""
增强的浏览器管理器 - 使用Selenium-Stealth + 随机化
防止浏览器指纹识别
"""
import os
import random
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

try:
    from selenium_stealth import stealth
    from fake_useragent import UserAgent
    STEALTH_AVAILABLE = True
except ImportError:
    print("⚠️ selenium-stealth 或 fake-useragent 未安装")
    print("请运行: pip install selenium-stealth fake-useragent")
    STEALTH_AVAILABLE = False

class StealthBrowserManager:
    """增强的浏览器管理器，支持指纹伪装"""

    def __init__(self, chrome_path=None, profiles_dir="browser_profiles"):
        self.chrome_path = chrome_path or self._find_chrome_path()
        self.profiles_dir = profiles_dir
        self.running_browsers = {}
        self.ua = UserAgent() if STEALTH_AVAILABLE else None

        # 确保配置目录存在
        os.makedirs(self.profiles_dir, exist_ok=True)

        # 预定义的随机化配置
        self.screen_resolutions = [
            (1920, 1080), (1366, 768), (1440, 900), (1536, 864),
            (1280, 720), (1600, 900), (1024, 768), (1680, 1050)
        ]

        self.languages = [
            ["en-US", "en"], ["en-GB", "en"], ["en-CA", "en"],
            ["zh-CN", "zh"], ["zh-TW", "zh"], ["ja-JP", "ja"]
        ]

        self.timezones = [
            "America/New_York", "America/Los_Angeles", "Europe/London",
            "Europe/Berlin", "Asia/Tokyo", "Asia/Shanghai", "Asia/Seoul"
        ]

        self.webgl_vendors = [
            "Intel Inc.", "NVIDIA Corporation", "AMD", "Apple Inc."
        ]

        self.webgl_renderers = [
            "Intel Iris OpenGL Engine", "NVIDIA GeForce GTX 1060",
            "AMD Radeon Pro 560", "Apple M1", "Intel HD Graphics 620"
        ]

    def _find_chrome_path(self):
        """查找Chrome可执行文件路径"""
        possible_paths = [
            "./138/chrome-win64/chrome.exe",  # 134文件夹中的Chrome
            "134\\chrome-win64\\chrome.exe",
            os.path.join(os.getcwd(), "138", "chrome-win64", "chrome.exe"),
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            "./browsers/chrome/chrome.exe"  # 便携版Chrome
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        return "chrome"  # 使用系统PATH中的chrome

    def generate_random_fingerprint(self):
        """生成随机的浏览器指纹配置"""
        fingerprint = {
            'user_agent': self.ua.random if self.ua else self._get_random_user_agent(),
            'screen_resolution': random.choice(self.screen_resolutions),
            'language': random.choice(self.languages),
            'timezone': random.choice(self.timezones),
            'webgl_vendor': random.choice(self.webgl_vendors),
            'webgl_renderer': random.choice(self.webgl_renderers),
            'platform': random.choice(['Win32', 'MacIntel', 'Linux x86_64']),
            'hardware_concurrency': random.choice([2, 4, 6, 8, 12, 16]),
            'device_memory': random.choice([2, 4, 8, 16, 32]),
            'color_depth': random.choice([24, 30, 32]),
            'pixel_ratio': random.choice([1, 1.25, 1.5, 2, 2.5, 3])
        }
        return fingerprint

    def _get_random_user_agent(self):
        """备用的随机User-Agent生成器"""
        chrome_versions = ['120.0.0.0', '119.0.0.0', '118.0.0.0', '117.0.0.0']
        webkit_versions = ['537.36', '537.35', '537.34']

        chrome_ver = random.choice(chrome_versions)
        webkit_ver = random.choice(webkit_versions)

        return f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{webkit_ver} (KHTML, like Gecko) Chrome/{chrome_ver} Safari/{webkit_ver}"

    def create_stealth_options(self, profile_name, fingerprint=None):
        """创建带有指纹伪装的Chrome选项"""
        if not fingerprint:
            fingerprint = self.generate_random_fingerprint()

        options = Options()

        # 基础配置
        profile_path = os.path.join(self.profiles_dir, profile_name)
        options.add_argument(f'--user-data-dir={profile_path}')

        # 反检测配置
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # 指纹伪装
        options.add_argument(f'--user-agent={fingerprint["user_agent"]}')
        width, height = fingerprint['screen_resolution']
        options.add_argument(f'--window-size={width},{height}')

        # 语言设置
        options.add_argument(f'--lang={fingerprint["language"][0]}')
        options.add_experimental_option('prefs', {
            'intl.accept_languages': ','.join(fingerprint['language'])
        })

        # WebGL伪装
        options.add_argument('--use-gl=swiftshader-webgl')
        options.add_argument('--disable-gpu-sandbox')

        # 其他隐私设置
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-ipc-flooding-protection')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-client-side-phishing-detection')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-default-apps')

        # 保存指纹配置
        self._save_fingerprint(profile_name, fingerprint)

        return options, fingerprint

    def _save_fingerprint(self, profile_name, fingerprint):
        """保存指纹配置到文件"""
        fingerprint_file = os.path.join(self.profiles_dir, profile_name, 'fingerprint.json')
        os.makedirs(os.path.dirname(fingerprint_file), exist_ok=True)

        with open(fingerprint_file, 'w', encoding='utf-8') as f:
            json.dump(fingerprint, f, indent=2, ensure_ascii=False)

    def launch_stealth_browser(self, profile_name):
        """启动带有指纹伪装的浏览器"""
        if not STEALTH_AVAILABLE:
            print("❌ Stealth功能不可用，请安装依赖")
            return None

        try:
            print(f"🚀 启动隐身浏览器: {profile_name}")

            # 生成随机指纹
            fingerprint = self.generate_random_fingerprint()
            print(f"🎭 生成指纹: {fingerprint['user_agent'][:50]}...")

            # 创建Chrome选项
            options, fingerprint = self.create_stealth_options(profile_name, fingerprint)

            # 启动浏览器
            service = Service(executable_path=self.chrome_path)
            driver = webdriver.Chrome(service=service, options=options)

            # 应用stealth模式
            stealth(driver,
                languages=fingerprint['language'],
                vendor=fingerprint['webgl_vendor'],
                platform=fingerprint['platform'],
                webgl_vendor=fingerprint['webgl_vendor'],
                renderer=fingerprint['webgl_renderer'],
                fix_hairline=True,
            )

            # 注入额外的指纹伪装脚本
            self._inject_fingerprint_script(driver, fingerprint)

            # 记录运行状态
            self.running_browsers[profile_name] = {
                'driver': driver,
                'fingerprint': fingerprint,
                'pid': driver.service.process.pid if driver.service.process else None
            }

            print(f"✅ 隐身浏览器启动成功: {profile_name}")
            return driver

        except Exception as e:
            print(f"❌ 启动隐身浏览器失败: {e}")
            return None

    def _inject_fingerprint_script(self, driver, fingerprint):
        """注入指纹伪装脚本"""
        script = f"""
        // 伪装硬件信息
        Object.defineProperty(navigator, 'hardwareConcurrency', {{
            get: () => {fingerprint['hardware_concurrency']}
        }});

        Object.defineProperty(navigator, 'deviceMemory', {{
            get: () => {fingerprint['device_memory']}
        }});

        // 伪装屏幕信息
        Object.defineProperty(screen, 'colorDepth', {{
            get: () => {fingerprint['color_depth']}
        }});

        Object.defineProperty(window, 'devicePixelRatio', {{
            get: () => {fingerprint['pixel_ratio']}
        }});

        // 移除webdriver标识
        Object.defineProperty(navigator, 'webdriver', {{
            get: () => undefined
        }});

        // 伪装插件信息
        Object.defineProperty(navigator, 'plugins', {{
            get: () => [{{
                name: 'Chrome PDF Plugin',
                filename: 'internal-pdf-viewer',
                description: 'Portable Document Format'
            }}]
        }});

        console.log('🎭 指纹伪装脚本已注入');
        """

        try:
            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': script
            })
        except Exception as e:
            print(f"⚠️ 注入指纹脚本失败: {e}")

    def test_fingerprint(self, profile_name, test_url="https://browserleaks.com/javascript"):
        """测试浏览器指纹"""
        driver = self.running_browsers.get(profile_name, {}).get('driver')
        if not driver:
            print(f"❌ 浏览器 {profile_name} 未运行")
            return None

        try:
            print(f"🔍 测试指纹: {test_url}")
            driver.get(test_url)
            time.sleep(3)

            # 获取页面标题作为测试结果
            title = driver.title
            print(f"📄 页面标题: {title}")

            return {
                'url': test_url,
                'title': title,
                'user_agent': driver.execute_script('return navigator.userAgent;'),
                'screen_resolution': driver.execute_script('return [screen.width, screen.height];'),
                'language': driver.execute_script('return navigator.language;'),
                'platform': driver.execute_script('return navigator.platform;'),
                'hardware_concurrency': driver.execute_script('return navigator.hardwareConcurrency;'),
                'device_memory': driver.execute_script('return navigator.deviceMemory;')
            }

        except Exception as e:
            print(f"❌ 指纹测试失败: {e}")
            return None

    def close_browser(self, profile_name):
        """关闭指定的浏览器"""
        if profile_name in self.running_browsers:
            try:
                driver = self.running_browsers[profile_name]['driver']
                driver.quit()
                del self.running_browsers[profile_name]
                print(f"✅ 浏览器已关闭: {profile_name}")
                return True
            except Exception as e:
                print(f"❌ 关闭浏览器失败: {e}")
                return False
        return False

    def close_all_browsers(self):
        """关闭所有浏览器"""
        for profile_name in list(self.running_browsers.keys()):
            self.close_browser(profile_name)

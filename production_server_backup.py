"""
生产级服务器配置
使用Waitress WSGI服务器替代Flask开发服务器，提供更好的稳定性和性能
"""
import os
import sys
import threading
import time
import json
from datetime import datetime
from waitress import serve
from flask import Flask, request, jsonify

# 导入必要的模块
from config_manager import ConfigManager
from browser_manager import BrowserManager
from automation_engine import AutomationEngine
from profile_usage_tracker import ProfileUsageTracker
from auth_manager import AuthManager
from sqlite_pool import get_sqlite_connection


class ProductionBackend:
    """生产级后端服务（无GUI版本）"""

    def __init__(self):
        self.app = None
        self.running = False

        # 加载配置
        self.load_config()

        # 初始化组件
        self.init_components()

        # 生产服务器不需要任务处理状态变量
        # 任务处理完全由 autoback.py 负责
        # self.is_processing = False
        # self.current_task = None
        # self.task_timeout_seconds = 300
        # self.task_start_time = None
        # self.timeout_check_enabled = True
        # self.task_check_enabled = True
        # self.task_check_triggered = False

        # 状态控制
        self.is_closing = False
        self.cleanup_completed = False

        print("✅ 生产后端初始化完成")

    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            print("✅ 配置文件加载成功")
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            # 使用默认配置
            self.config = {
                'api': {'host': '127.0.0.1', 'port': 5001},
                'browser': {'executable_path': '134/chrome-win64/chrome.exe'}
            }

    def init_components(self):
        """初始化各个组件"""
        try:
            # 数据库配置（与 autoback.py 保持一致）
            self.mysql_config = {
                'host': 'localhost',
                'database': 'activeaug',
                'user': 'root',
                'password': '',
                'charset': 'utf8mb4',
                'autocommit': True,
                'connection_timeout': 15,
                'pool_reset_session': True,
                'use_unicode': True,
                'raise_on_warnings': False  # 降低警告级别
            }

            self.jihuoma_config = {
                'host': '**************',
                'database': 'jihuoma',
                'user': 'jihuoma',
                'password': '123123',
                'charset': 'utf8mb4',
                'autocommit': True,
                'connection_timeout': 15,
                'pool_reset_session': True,
                'use_unicode': True
            }

            # 初始化认证管理器（生产服务器专用配置）
            # 🔥 为生产服务器配置更大的连接池
            production_mysql_config = self.mysql_config.copy()
            production_mysql_config.update({
                'pool_size': 5,  # 🔥 增加到5个连接（vs autoback.py的2个）
                'connection_timeout': 10,  # 🔥 减少超时时间，快速失败
            })

            self.auth_manager = AuthManager(
                main_db_config=production_mysql_config,
                jihuoma_db_config=self.jihuoma_config,
                jwt_secret="augment-system-secret-key-2025"
            )

            print("✅ 生产服务器认证管理器初始化完成 (连接池: 5个)")

            # 生产服务器只需要认证管理器，其他组件由 autoback.py 负责
            # self.config_manager = ConfigManager()
            # self.browser_manager = BrowserManager(self.config_manager)
            # self.automation_engine = AutomationEngine(self.browser_manager)
            # self.usage_tracker = ProfileUsageTracker()

            print("✅ 组件初始化完成")
        except Exception as e:
            print(f"❌ 组件初始化失败: {e}")
            raise

    def log(self, message):
        """无GUI日志方法"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")

    def init_database(self):
        """初始化数据库"""
        try:
            with get_sqlite_connection('tasks.db') as conn:
                # 创建任务队列表（与 autoback.py 保持一致）
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS task_queue (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_token VARCHAR(100) NOT NULL,
                        user_id INTEGER NULL,
                        email VARCHAR(255) NOT NULL,
                        status VARCHAR(20) DEFAULT 'waiting',
                        queue_position INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        started_at TIMESTAMP NULL,
                        completed_at TIMESTAMP NULL,
                        result_message TEXT NULL,
                        processing_details TEXT NULL
                    )
                ''')

                # 添加缺失的列（如果不存在）
                try:
                    conn.execute('ALTER TABLE task_queue ADD COLUMN queue_position INTEGER')
                except:
                    pass  # 列已存在
                try:
                    conn.execute('ALTER TABLE task_queue ADD COLUMN started_at TIMESTAMP NULL')
                except:
                    pass  # 列已存在
                try:
                    conn.execute('ALTER TABLE task_queue ADD COLUMN processing_details TEXT NULL')
                except:
                    pass  # 列已存在

                # 检查系统状态表是否存在
                cursor = conn.execute('''
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='system_status'
                ''')
                table_exists = cursor.fetchone() is not None

                if table_exists:
                    # 检查是否有 last_updated 列
                    cursor = conn.execute('PRAGMA table_info(system_status)')
                    columns = [row[1] for row in cursor.fetchall()]

                    if 'last_updated' not in columns:
                        # 添加缺失的列（SQLite不支持CURRENT_TIMESTAMP作为默认值，使用NULL）
                        self.log("🔧 升级系统状态表结构...")
                        conn.execute('''
                            ALTER TABLE system_status
                            ADD COLUMN last_updated TIMESTAMP
                        ''')
                        # 更新现有记录的时间戳
                        conn.execute('''
                            UPDATE system_status
                            SET last_updated = CURRENT_TIMESTAMP
                            WHERE last_updated IS NULL
                        ''')
                        self.log("✅ 系统状态表升级完成")
                else:
                    # 创建新的系统状态表
                    conn.execute('''
                        CREATE TABLE system_status (
                            id INTEGER PRIMARY KEY,
                            is_processing BOOLEAN DEFAULT 0,
                            current_processing_task_id INTEGER NULL,
                            current_email VARCHAR(255) NULL,
                            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    ''')

                # 确保有一条系统状态记录
                cursor = conn.execute('SELECT COUNT(*) FROM system_status WHERE id = 1')
                count = cursor.fetchone()[0]

                if count == 0:
                    conn.execute('''
                        INSERT INTO system_status (id, is_processing, current_processing_task_id, current_email, last_updated)
                        VALUES (1, 0, NULL, NULL, CURRENT_TIMESTAMP)
                    ''')
                else:
                    # 更新现有记录，确保所有字段都有值
                    conn.execute('''
                        UPDATE system_status
                        SET is_processing = COALESCE(is_processing, 0),
                            current_processing_task_id = NULL,
                            current_email = NULL,
                            last_updated = CURRENT_TIMESTAMP
                        WHERE id = 1
                    ''')

                # 立即提交以确保记录被保存
                conn.commit()

            self.log("✅ 数据库初始化完成")
        except Exception as e:
            self.log(f"❌ 数据库初始化失败: {e}")
            raise

    def start_server(self, host='127.0.0.1', port=5001, threads=6):
        """启动生产服务器"""
        try:
            app = self.create_app()
            if not app:
                return False

            print(f"🚀 启动生产服务器...")
            print(f"📍 地址: http://{host}:{port}")
            print(f"🧵 线程数: {threads}")
            print(f"💡 使用Waitress WSGI服务器")

            # 使用Waitress服务器
            serve(
                app,
                host=host,
                port=port,
                threads=threads,
                connection_limit=1000,
                cleanup_interval=30,
                channel_timeout=120,
                log_socket_errors=True,
                max_request_body_size=1073741824,  # 1GB
                expose_tracebacks=False
            )

        except KeyboardInterrupt:
            print("\n🔄 接收到中断信号")
            self.stop_server()
        except Exception as e:
            print(f"❌ 服务器启动失败: {e}")
            return False

    def init_flask_app(self):
        """初始化Flask应用和路由"""
        self.app = Flask(__name__)
        self.app.config['JSON_AS_ASCII'] = False

        # 基本测试路由
        @self.app.route('/api/test', methods=['GET', 'POST'])
        def test_api():
            if request.method == 'POST':
                data = request.get_json() or {}
                return jsonify({
                    'success': True,
                    'message': 'POST request received!',
                    'data': data,
                    'timestamp': time.time(),
                    'tunnel_compatible': True
                })
            else:
                return jsonify({
                    'success': True,
                    'message': 'API is working!',
                    'timestamp': time.time(),
                    'tunnel_compatible': True
                })

        # 任务提交路由
        @self.app.route('/api/submit', methods=['POST'])
        def submit_task():
            return self.handle_submit_task()

        # 系统状态路由 - 生产环境需要
        @self.app.route('/api/system/status', methods=['GET'])
        def system_status():
            return self.handle_system_status()

        # 认证相关路由
        @self.app.route('/api/auth/login', methods=['POST'])
        def auth_login():
            return self.handle_auth_login()

        @self.app.route('/api/auth/register', methods=['POST'])
        def auth_register():
            return self.handle_auth_register()

        @self.app.route('/api/auth/check', methods=['POST'])
        def auth_check():
            return self.handle_auth_check()

        # 任务状态路由 - 生产环境需要
        @self.app.route('/api/task/<int:task_id>/status', methods=['GET'])
        def get_task_status(task_id):
            return self.handle_get_task_status(task_id)

        # 队列列表路由 - 生产环境需要
        @self.app.route('/api/queue/list', methods=['GET'])
        def get_queue_list():
            return self.handle_get_queue_list()

        # 清除缓存路由 - 调试用
        @self.app.route('/api/admin/clear-cache', methods=['POST'])
        def clear_cache():
            return self.handle_clear_cache()

        # 用户信息路由
        @self.app.route('/api/auth/profile', methods=['GET'])
        def get_profile():
            return self.handle_get_profile()

        # 激活码充值路由
        @self.app.route('/api/recharge/activate', methods=['POST'])
        def activate_code():
            return self.handle_activate_code()

        # 充值URL路由
        @self.app.route('/api/config/recharge-url', methods=['GET'])
        def get_recharge_url():
            return self.handle_get_recharge_url()

        # 用户提交历史路由
        @self.app.route('/api/user/submissions', methods=['GET'])
        def get_user_submissions():
            return self.handle_get_user_submissions()

        self.log("✅ Flask应用路由初始化完成")

    def handle_submit_task(self):
        """处理任务提交（需要用户认证）"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            # 验证用户认证
            auth_header = request.headers.get('Authorization')

            try:
                user_info = self.auth_manager.require_auth(auth_header)
                if not user_info:
                    return jsonify({'success': False, 'message': '请先登录'}), 401

                self.log(f"✅ 用户认证成功: {user_info['username']}")
            except Exception as auth_error:
                self.log(f"❌ 认证过程异常: {auth_error}")
                return jsonify({'success': False, 'message': '认证服务暂时不可用，请稍后重试'}), 503

            # 检查用户剩余次数
            try:
                if not self.auth_manager.check_user_quota(user_info['id']):
                    self.log(f"❌ 用户 {user_info['username']} 剩余次数不足")
                    return jsonify({'success': False, 'message': '剩余次数不足，请充值后再试'}), 403
            except Exception as quota_error:
                self.log(f"⚠️ 检查用户配额异常: {quota_error}")
                # 配额检查失败时，允许继续提交，但记录警告
                self.log("⚠️ 配额检查失败，允许任务提交")

            data = request.get_json()
            email = data.get('email', '').strip()

            # 验证邮箱
            if not email or not self.validate_email(email):
                return jsonify({'success': False, 'message': '邮箱格式无效'}), 400

            # 生成用户标识（包含用户ID）
            import time
            user_token = f"user_{user_info['id']}_{int(time.time())}"

            # 添加任务到队列
            try:
                with get_sqlite_connection('tasks.db') as conn:
                    cursor = conn.execute('''
                        INSERT INTO task_queue (user_token, user_id, email, status)
                        VALUES (?, ?, ?, 'waiting')
                    ''', (user_token, user_info['id'], email))
                    task_id = cursor.lastrowid

                    # 获取队列位置
                    cursor = conn.execute('''
                        SELECT COUNT(*) FROM task_queue
                        WHERE status = 'waiting' AND id <= ?
                    ''', (task_id,))
                    queue_position = cursor.fetchone()[0]
            except Exception as db_error:
                # 🔥 增加详细的错误日志
                error_msg = str(db_error).strip()
                if error_msg and error_msg != '0':
                    self.log(f"❌ SQLite数据库操作失败: {db_error}")
                    self.log(f"❌ 错误类型: {type(db_error)}")
                else:
                    self.log(f"❌ SQLite数据库操作失败: 未知错误 (错误信息为空或0)")
                return jsonify({'success': False, 'message': '数据库服务暂时不可用，请稍后重试'}), 503

            # 🔥 保存用户提交历史（不阻止任务提交）
            try:
                self.save_user_submission(user_info['id'], email, task_id, queue_position, request.remote_addr)
            except Exception as save_error:
                # 🔥 增加详细的错误日志，但不阻止任务提交
                error_msg = str(save_error).strip()
                if error_msg and error_msg != '0':
                    self.log(f"⚠️ 保存用户提交历史失败: {save_error}")
                    self.log(f"⚠️ 错误类型: {type(save_error)}")
                else:
                    self.log(f"⚠️ 保存用户提交历史失败: 未知错误 (错误信息为空或0)")
                self.log(f"💡 任务已成功提交到队列，仅提交历史保存失败")

            # 通知管理界面有新任务（不阻塞任务提交）
            try:
                self.notify_management_gui_new_task()
            except Exception as notify_error:
                self.log(f"⚠️ 通知管理界面失败，但任务已成功提交: {notify_error}")

            self.log(f"📧 用户 {user_info['username']} 提交请求: {email}")

            return jsonify({
                'success': True,
                'task_id': task_id,
                'queue_position': queue_position,
                'message': f'任务已提交，排队第{queue_position}位',
                'user_quota': user_info['time_quota']
            }), 200

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.log(f"❌ 任务提交失败: {e}")
            self.log(f"❌ 详细错误信息: {error_details}")
            return jsonify({'success': False, 'message': f'任务提交失败: {str(e)}'}), 500

    def handle_system_status(self):
        """处理系统状态查询（从数据库获取最新状态）"""
        try:
            with get_sqlite_connection('tasks.db') as conn:
                # 设置行工厂以支持字典式访问
                conn.row_factory = lambda cursor, row: dict(zip([col[0] for col in cursor.description], row))

                # 获取系统状态
                cursor = conn.execute('SELECT * FROM system_status WHERE id = 1')
                system_status = cursor.fetchone()

                # 获取队列统计（等待中的任务数量）
                cursor = conn.execute('SELECT COUNT(*) as count FROM task_queue WHERE status = "waiting"')
                queue_result = cursor.fetchone()
                queue_length = queue_result['count'] if queue_result else 0

                # 获取可用配置数量（缓存结果，避免频繁计算）
                current_time = time.time()
                if not hasattr(self, '_cached_available_count') or not hasattr(self, '_last_profile_check') or \
                   current_time - self._last_profile_check > 60:  # 1分钟缓存
                    self.log(f"🔄 刷新配置缓存...")
                    available_profiles = self.get_available_profiles()
                    self._cached_available_count = len(available_profiles)
                    self._last_profile_check = current_time
                    self.log(f"✅ 配置缓存已更新: {self._cached_available_count}")
                else:
                    self.log(f"📊 使用缓存的配置数: {self._cached_available_count}")

                available_count = self._cached_available_count

                # 不显示具体的邮箱地址，保护隐私
                current_email = None
                if system_status and system_status.get('current_email'):
                    email = system_status['current_email']
                    if '@' in email:
                        local, domain = email.split('@', 1)
                        current_email = f"{local[:2]}***@{domain}"
                    else:
                        current_email = "***"

                # 返回与 autoback.py 一致的数据结构
                return jsonify({
                    'success': True,
                    'is_processing': bool(system_status['is_processing']) if system_status else False,
                    'current_email': current_email,
                    'started_at': system_status.get('last_updated') if system_status else None,
                    'queue_length': queue_length,
                    'available_profiles': available_count
                }), 200

        except Exception as e:
            self.log(f"❌ 获取系统状态失败: {e}")
            return jsonify({'success': False, 'message': '获取系统状态失败'}), 500

    def check_management_gui_running(self):
        """检查管理界面是否运行"""
        try:
            import requests
            response = requests.get('http://localhost:5002/api/test', timeout=2)
            return response.status_code == 200
        except:
            return False

    def handle_auth_login(self):
        """处理登录请求"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            data = request.get_json()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()

            if not username or not password:
                return jsonify({'success': False, 'message': '用户名和密码不能为空'}), 400

            # 验证用户
            result = self.auth_manager.login_user(username, password)

            if result['success']:
                return jsonify({
                    'success': True,
                    'message': '登录成功',
                    'token': result['token'],
                    'user': result['user']
                })
            else:
                return jsonify({
                    'success': False,
                    'message': result['message']
                }), 401

        except Exception as e:
            self.log(f"❌ 登录失败: {e}")
            return jsonify({'success': False, 'message': '登录失败'}), 500

    def handle_auth_register(self):
        """处理注册请求"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            data = request.get_json()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()

            if not username or not password:
                return jsonify({'success': False, 'message': '用户名和密码不能为空'}), 400

            # 注册用户
            result = self.auth_manager.register_user(username, password)

            if result['success']:
                return jsonify({
                    'success': True,
                    'message': result['message'],
                    'gift_quota': result.get('gift_quota', 0)
                })
            else:
                return jsonify({
                    'success': False,
                    'message': result['message']
                }), 400

        except Exception as e:
            self.log(f"❌ 注册失败: {e}")
            return jsonify({'success': False, 'message': '注册失败'}), 500

    def handle_auth_check(self):
        """处理认证检查请求"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            data = request.get_json()
            token = data.get('token', '')

            if not token:
                return jsonify({
                    'success': False,
                    'authenticated': False,
                    'message': '缺少认证令牌'
                }), 400

            # 验证令牌
            user_info = self.auth_manager.require_auth(token)

            if user_info:
                return jsonify({
                    'success': True,
                    'authenticated': True,
                    'user': {
                        'id': user_info['id'],
                        'username': user_info['username'],
                        'time_quota': user_info.get('time_quota', 0)
                    }
                }), 200
            else:
                return jsonify({
                    'success': False,
                    'authenticated': False,
                    'message': '无效的认证令牌'
                }), 401

        except Exception as e:
            self.log(f"❌ 认证检查失败: {e}")
            return jsonify({
                'success': False,
                'authenticated': False,
                'message': '认证检查失败'
            }), 500

    def handle_get_task_status(self, task_id):
        """处理任务状态查询（从数据库获取最新状态）"""
        try:
            # 验证用户认证
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)
            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            with get_sqlite_connection('tasks.db') as conn:
                # 设置行工厂以支持字典式访问
                conn.row_factory = lambda cursor, row: dict(zip([col[0] for col in cursor.description], row))

                cursor = conn.execute('''
                    SELECT * FROM task_queue WHERE id = ? AND user_id = ?
                ''', (task_id, user_info['id']))
                task = cursor.fetchone()

                if not task:
                    return jsonify({'success': False, 'message': '任务不存在或无权访问'}), 404

                # 转换状态名称以匹配前端期望
                status_map = {
                    'waiting': 'pending',
                    'processing': 'processing',
                    'completed': 'success',
                    'failed': 'failed'
                }

                return jsonify({
                    'success': True,
                    'task_id': task_id,
                    'email': task['email'],
                    'status': status_map.get(task['status'], task['status']),
                    'created_at': task['created_at'],
                    'completed_at': task['completed_at'],
                    'result_message': task['result_message']
                }), 200

        except Exception as e:
            self.log(f"❌ 获取任务状态失败: {e}")
            return jsonify({'success': False, 'message': '查询失败'}), 500

    def handle_get_queue_list(self):
        """处理队列列表查询（从数据库获取最新状态）"""
        try:
            # 验证用户认证
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)
            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            with get_sqlite_connection('tasks.db') as conn:
                cursor = conn.execute('''
                    SELECT id, email, status, created_at, completed_at, result_message
                    FROM task_queue
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                    LIMIT 50
                ''', (user_info['id'],))
                tasks = cursor.fetchall()

            task_list = []
            for task in tasks:
                task_dict = dict(task) if hasattr(task, 'keys') else {
                    'id': task[0], 'email': task[1], 'status': task[2],
                    'created_at': task[3], 'completed_at': task[4], 'result_message': task[5]
                }
                task_list.append(task_dict)

            return jsonify({
                'success': True,
                'queue': task_list,
                'total_count': len(task_list)
            })

        except Exception as e:
            self.log(f"❌ 获取队列列表失败: {e}")
            return jsonify({'success': False, 'message': '获取队列列表失败'}), 500

    def handle_get_profile(self):
        """处理获取用户信息请求"""
        try:
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)

            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            return jsonify({
                'success': True,
                'user': user_info
            }), 200

        except Exception as e:
            self.log(f"❌ 获取用户信息失败: {e}")
            return jsonify({'success': False, 'message': '获取用户信息失败'}), 500

    def handle_activate_code(self):
        """处理激活码充值"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            # 验证用户认证
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)

            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            data = request.get_json()
            activation_code = data.get('activation_code', '').strip()

            if not activation_code:
                return jsonify({'success': False, 'message': '激活码不能为空'}), 400

            result = self.auth_manager.recharge_with_activation_code(user_info['id'], activation_code)

            if result['success']:
                self.log(f"💰 用户 {user_info['username']} 充值成功: {activation_code}")
                return jsonify(result), 200
            else:
                return jsonify(result), 400

        except Exception as e:
            self.log(f"❌ 激活码充值失败: {e}")
            return jsonify({'success': False, 'message': '激活码充值失败'}), 500

    def handle_get_recharge_url(self):
        """处理获取充值URL请求"""
        try:
            # 使用 AuthManager 的方法获取激活码购买链接
            url = self.auth_manager.get_activation_code_url()
            return jsonify({
                'success': True,
                'url': url,
                'recharge_url': url  # 保持兼容性
            })

        except Exception as e:
            self.log(f"❌ 获取充值URL失败: {e}")
            return jsonify({'success': False, 'message': '获取充值URL失败'}), 500

    def handle_get_user_submissions(self):
        """处理获取用户提交历史请求"""
        try:
            # 验证用户认证
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)

            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            # 获取分页参数
            page = int(request.args.get('page', 1))
            limit = int(request.args.get('limit', 20))
            status_filter = request.args.get('status', '')

            offset = (page - 1) * limit

            # 使用MySQL连接获取用户提交历史
            connection = self.auth_manager.get_main_db_connection()
            if not connection:
                return jsonify({'success': False, 'message': '数据库连接失败'}), 500

            try:
                cursor = connection.cursor(dictionary=True)

                # 构建查询条件
                where_conditions = ["user_id = %s"]
                params = [user_info['id']]

                if status_filter:
                    where_conditions.append("status = %s")
                    params.append(status_filter)

                where_clause = " AND ".join(where_conditions)

                # 获取总数
                count_query = f"SELECT COUNT(*) as total FROM user_submissions WHERE {where_clause}"
                cursor.execute(count_query, params)
                total_count = cursor.fetchone()['total']

                # 获取分页数据
                query = f"""
                    SELECT id, email, task_id, queue_position, ip_address, submitted_at, status
                    FROM user_submissions
                    WHERE {where_clause}
                    ORDER BY submitted_at DESC
                    LIMIT %s OFFSET %s
                """
                params.extend([limit, offset])
                cursor.execute(query, params)
                submissions = cursor.fetchall()

                return jsonify({
                    'success': True,
                    'data': {
                        'submissions': submissions,
                        'pagination': {
                            'page': page,
                            'limit': limit,
                            'total': total_count,
                            'pages': (total_count + limit - 1) // limit
                        }
                    },
                    'submissions': submissions,  # 保持兼容性
                    'pagination': {
                        'page': page,
                        'limit': limit,
                        'total': total_count,
                        'pages': (total_count + limit - 1) // limit
                    }
                })

            finally:
                cursor.close()
                connection.close()

        except Exception as e:
            self.log(f"❌ 获取用户提交历史失败: {e}")
            return jsonify({'success': False, 'message': '获取用户提交历史失败'}), 500

    def validate_email(self, email):
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def save_user_submission(self, user_id, email, task_id, queue_position, ip_address):
        """保存用户提交记录到历史表"""
        try:
            connection = self.auth_manager.get_main_db_connection()
            if not connection:
                raise Exception("无法获取数据库连接")

            cursor = connection.cursor()

            # 获取北京时间
            from datetime import datetime, timezone, timedelta
            beijing_tz = timezone(timedelta(hours=8))
            beijing_time = datetime.now(beijing_tz)

            insert_query = """
                INSERT INTO user_submissions (user_id, email, task_id, queue_position, ip_address, submitted_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (user_id, email, task_id, queue_position, ip_address, beijing_time))
            connection.commit()

            self.log(f"📝 用户 {user_id} 提交记录已保存: {email}")

        except Exception as e:
            # 🔥 增加详细的错误日志
            error_msg = str(e).strip()
            if error_msg and error_msg != '0':
                self.log(f"❌ 保存用户提交记录失败: {e}")
                self.log(f"❌ 错误类型: {type(e)}")
            else:
                self.log(f"❌ 保存用户提交记录失败: 未知错误 (错误信息为空或0)")
            raise  # 重新抛出异常，让调用者知道保存失败
        finally:
            if 'connection' in locals() and connection and connection.is_connected():
                cursor.close()
                connection.close()

    def get_available_profiles(self):
        """获取可用配置数量（优先从Excel获取真实数据）"""
        try:
            # 优先从 Excel 使用记录获取准确数据
            excel_file = "profile_usage.xlsx"
            self.log(f"🔍 检查Excel文件: {excel_file}")

            if os.path.exists(excel_file):
                self.log(f"✅ Excel文件存在")
                try:
                    import pandas as pd
                    df = pd.read_excel(excel_file)
                    self.log(f"✅ 成功读取Excel，行数: {len(df)}")

                    if '配置名称' in df.columns and '状态' in df.columns:
                        # 统计未使用的配置
                        unused_profiles = df[df['状态'] == '未使用']['配置名称'].tolist()
                        self.log(f"✅ 找到 {len(unused_profiles)} 个未使用配置")
                        return unused_profiles
                    else:
                        self.log(f"❌ Excel缺少必要列，现有列: {list(df.columns)}")
                except Exception as excel_error:
                    self.log(f"❌ Excel读取失败: {excel_error}")
            else:
                self.log(f"❌ Excel文件不存在: {excel_file}")

            # 备用方案：检查本地配置目录
            config_dir = "profiles"
            self.log(f"🔍 检查配置目录: {config_dir}")

            if os.path.exists(config_dir):
                try:
                    profile_files = [f for f in os.listdir(config_dir) if f.endswith('.json')]
                    self.log(f"✅ 配置目录有 {len(profile_files)} 个文件")
                    return profile_files
                except Exception as dir_error:
                    self.log(f"❌ 配置目录读取失败: {dir_error}")
            else:
                self.log(f"❌ 配置目录不存在: {config_dir}")

            # 默认返回空列表
            self.log(f"⚠️ 所有方法都失败，返回空列表")
            return []

        except Exception as e:
            self.log(f"❌ get_available_profiles异常: {e}")
            return []

    def clear_profile_cache(self):
        """清除配置缓存"""
        if hasattr(self, '_cached_available_count'):
            delattr(self, '_cached_available_count')
        if hasattr(self, '_last_profile_check'):
            delattr(self, '_last_profile_check')
        self.log("🧹 配置缓存已清除")

    def handle_clear_cache(self):
        """处理清除缓存请求"""
        try:
            self.clear_profile_cache()
            return jsonify({'success': True, 'message': '缓存已清除'}), 200
        except Exception as e:
            self.log(f"❌ 清除缓存失败: {e}")
            return jsonify({'success': False, 'message': '清除缓存失败'}), 500


    def start_task_processor(self):
        """生产服务器不需要任务处理器，任务由 autoback.py 处理"""
        self.log("ℹ️ 生产服务器模式：任务处理由管理界面负责")

    def start_timeout_checker(self):
        """生产服务器不需要超时检查器"""
        self.log("ℹ️ 生产服务器模式：超时检查由管理界面负责")

    def task_processing_loop(self):
        """生产服务器不需要任务处理循环"""
        self.log("ℹ️ 生产服务器模式：跳过任务处理循环，任务由管理界面处理")



    def get_next_task_if_available(self):
        """生产服务器不获取任务"""
        self.log("ℹ️ 生产服务器模式：任务获取由管理界面负责")
        return None

    def process_task(self, task):
        """处理任务（转发给 autoback.py）"""
        task_dict = dict(task) if hasattr(task, 'keys') else task

        try:
            self.log(f"📤 转发任务给管理界面: {task_dict['email']}")

            # 通知 autoback.py 处理任务
            result = self.execute_task_logic(task_dict)

            if result['success']:
                self.log(f"✅ 任务已转发: {task_dict['email']}")
                # 生产服务器不更新任务状态，完全由 autoback.py 负责
            else:
                self.log(f"⚠️ 任务转发失败: {task_dict['email']} - {result['message']}")
                # 即使转发失败，也不更新任务状态，让 autoback.py 的定期检查来处理

        except Exception as e:
            self.log(f"❌ 处理任务异常: {e}")
            # 生产服务器不更新任务状态，避免与 autoback.py 冲突

    def execute_task_logic(self, task_dict):
        """通知 autoback.py 处理任务"""
        try:
            # 生产服务器不直接处理任务，而是通知 autoback.py 处理
            self.log(f"📤 通知管理界面处理任务: {task_dict['email']}")

            # 尝试通知 autoback.py 有新任务
            self.notify_management_gui_new_task()

            # 任务已经在数据库中，autoback.py 会自动获取并处理
            # 这里只是触发检查，不等待结果
            return {
                'success': True,
                'message': '任务已转发给处理系统'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'任务转发失败: {str(e)}'
            }

    def notify_management_gui_new_task(self):
        """通知管理界面有新任务（不阻塞主流程）"""
        try:
            import requests
            # 发送通知给 autoback.py，告诉它有新任务
            response = requests.post('http://localhost:5002/api/internal/task-notification',
                                   json={'action': 'new_task'},
                                   timeout=1)  # 减少超时时间
            if response.status_code == 200:
                self.log("✅ 已通知管理界面处理新任务")
            else:
                self.log(f"⚠️ 通知管理界面失败: {response.status_code}")
        except Exception as e:
            self.log(f"⚠️ 无法通知管理界面: {e}")
            # 即使通知失败，任务也已成功提交，autoback.py 会定期检查任务队列

    # 生产服务器不应该直接更新任务状态
    # 所有任务状态更新由 autoback.py 负责

    def complete_task(self, task_id, message):
        """生产服务器不更新任务状态"""
        self.log(f"ℹ️ 生产服务器模式：任务状态更新由管理界面负责")

    def fail_task(self, task_id, error_message):
        """生产服务器不更新任务状态"""
        self.log(f"ℹ️ 生产服务器模式：任务状态更新由管理界面负责")

    def force_complete_current_task(self, reason):
        """生产服务器不强制完成任务"""
        self.log(f"ℹ️ 生产服务器模式：任务管理由管理界面负责")

    def cleanup_resources(self):
        """清理资源"""
        try:
            self.log("🧹 清理资源...")
            self.task_check_enabled = False
            self.timeout_check_enabled = False
            self.is_closing = True

            if hasattr(self, 'browser_manager'):
                # 清理浏览器资源
                pass

            self.log("✅ 资源清理完成")
        except Exception as e:
            self.log(f"❌ 资源清理失败: {e}")


class ProductionServer:
    """生产级服务器管理器"""

    def __init__(self):
        self.backend = None
        self.running = False

    def create_app(self):
        """创建Flask应用实例"""
        try:
            # 创建后端实例
            self.backend = ProductionBackend()

            # 初始化数据库
            self.backend.init_database()

            # 初始化Flask应用
            self.backend.init_flask_app()

            # 生产服务器不需要启动任务处理器
            # 任务处理由 autoback.py 负责
            # self.backend.start_task_processor()
            # self.backend.start_timeout_checker()

            print("✅ Flask应用初始化完成")
            return self.backend.app

        except Exception as e:
            print(f"❌ 创建应用失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def stop_server(self):
        """停止服务器"""
        try:
            print("🔄 正在停止服务器...")

            if self.backend:
                self.backend.cleanup_resources()

            print("✅ 服务器已停止")

        except Exception as e:
            print(f"⚠️ 停止服务器时出错: {e}")


def install_waitress():
    """安装Waitress服务器"""
    try:
        import waitress
        print("✅ Waitress已安装")
        return True
    except ImportError:
        print("📦 正在安装Waitress...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'waitress'])
            print("✅ Waitress安装成功")
            return True
        except Exception as e:
            print(f"❌ Waitress安装失败: {e}")
            print("💡 请手动安装: pip install waitress")
            return False


def main():
    """主函数"""
    print("🚀 生产级autoback.py服务器")
    print("=" * 50)

    # 检查并安装Waitress
    if not install_waitress():
        return

    # 创建服务器实例
    server = ProductionServer()

    try:
        # 创建应用
        app = server.create_app()
        if not app:
            print("❌ 应用创建失败")
            return

        print(f"🚀 启动生产服务器...")
        print(f"📍 地址: http://127.0.0.1:5001")
        print(f"🧵 线程数: 6")
        print(f"💡 使用Waitress WSGI服务器")
        print(f"🎯 无GUI模式，专注于API服务")

        # 使用Waitress服务器
        serve(
            app,
            host='0.0.0.0',  # 监听所有接口，允许外部连接
            port=5001,
            threads=6,
            connection_limit=1000,
            cleanup_interval=30,
            channel_timeout=120,
            log_socket_errors=True,
            max_request_body_size=1073741824,  # 1GB
            expose_tracebacks=False
        )

    except KeyboardInterrupt:
        print("\n🔄 用户中断")
    except Exception as e:
        print(f"❌ 服务器异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        server.stop_server()


if __name__ == "__main__":
    main()

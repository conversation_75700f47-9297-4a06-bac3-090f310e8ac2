
// US Timezone & Location Spoofer - Enhanced Version v2.7.2
(function() {
    'use strict';

    // Detect sensitive websites to avoid breaking functionality
    const currentHost = window.location.hostname.toLowerCase();
    // Remove augmentcode.com from sensitive hosts to allow full functionality
    const sensitiveHosts = ['github.com', 'stackoverflow.com', 'developer.mozilla.org'];
    const isSensitiveHost = sensitiveHosts.some(host => currentHost.includes(host));

    if (isSensitiveHost) {
        console.log('🛡️ Sensitive website detected, skipping some features:', currentHost);
    } else {
        console.log('🌍 Full spoofing enabled for:', currentHost);
        console.log('🔧 All timezone, geolocation, and network interception features active');
    }

    // Immediately execute geolocation pre-override to prevent other scripts from interfering
    if (navigator.geolocation) {
        const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
        console.log('🔧 Pre-overriding geolocation API to prevent interference from other scripts');
    }

    // US major cities database
    const US_CITIES_DATABASE = {
        "America/New_York": [
            {city: "New York", state: "NY", lat: 40.7128, lng: -74.0060},
            {city: "Boston", state: "MA", lat: 42.3601, lng: -71.0589},
            {city: "Philadelphia", state: "PA", lat: 39.9526, lng: -75.1652},
            {city: "Atlanta", state: "GA", lat: 33.7490, lng: -84.3880},
            {city: "Miami", state: "FL", lat: 25.7617, lng: -80.1918},
            {city: "Washington", state: "DC", lat: 38.9072, lng: -77.0369},
            {city: "Charlotte", state: "NC", lat: 35.2271, lng: -80.8431},
            {city: "Jacksonville", state: "FL", lat: 30.3322, lng: -81.6557}
        ],
        "America/Chicago": [
            {city: "Chicago", state: "IL", lat: 41.8781, lng: -87.6298},
            {city: "Houston", state: "TX", lat: 29.7604, lng: -95.3698},
            {city: "Dallas", state: "TX", lat: 32.7767, lng: -96.7970},
            {city: "San Antonio", state: "TX", lat: 29.4241, lng: -98.4936},
            {city: "Austin", state: "TX", lat: 30.2672, lng: -97.7431},
            {city: "Minneapolis", state: "MN", lat: 44.9778, lng: -93.2650},
            {city: "Kansas City", state: "MO", lat: 39.0997, lng: -94.5786},
            {city: "Nashville", state: "TN", lat: 36.1627, lng: -86.7816}
        ],
        "America/Denver": [
            {city: "Denver", state: "CO", lat: 39.7392, lng: -104.9903},
            {city: "Phoenix", state: "AZ", lat: 33.4484, lng: -112.0740},
            {city: "Salt Lake City", state: "UT", lat: 40.7608, lng: -111.8910},
            {city: "Albuquerque", state: "NM", lat: 35.0844, lng: -106.6504},
            {city: "Colorado Springs", state: "CO", lat: 38.8339, lng: -104.8214},
            {city: "Mesa", state: "AZ", lat: 33.4152, lng: -111.8315},
            {city: "Tucson", state: "AZ", lat: 32.2226, lng: -110.9747}
        ],
        "America/Los_Angeles": [
            {city: "Los Angeles", state: "CA", lat: 34.0522, lng: -118.2437},
            {city: "San Francisco", state: "CA", lat: 37.7749, lng: -122.4194},
            {city: "Seattle", state: "WA", lat: 47.6062, lng: -122.3321},
            {city: "San Diego", state: "CA", lat: 32.7157, lng: -117.1611},
            {city: "Las Vegas", state: "NV", lat: 36.1699, lng: -115.1398},
            {city: "Portland", state: "OR", lat: 45.5152, lng: -122.6784},
            {city: "Sacramento", state: "CA", lat: 38.5816, lng: -121.4944},
            {city: "San Jose", state: "CA", lat: 37.3382, lng: -121.8863}
        ],
        "America/Adak": [
            {city: "Adak", state: "AK", lat: 51.8800, lng: -176.6581},
            {city: "Attu Station", state: "AK", lat: 52.8481, lng: 173.1778}
        ]
    };

    // Fixed selection: Always use New York for consistency
    const selectedTimezone = "America/New_York";
    const selectedCity = {city: "New York", state: "NY", lat: 40.7128, lng: -74.0060};

    // Add random offset to simulate real location variation
    const latOffset = (Math.random() - 0.5) * 0.2; // ±0.1 degrees
    const lngOffset = (Math.random() - 0.5) * 0.2; // ±0.1 degrees
    const accuracyVariation = Math.floor(Math.random() * 50) + 20; // 20-70 meters

    // Timezone offset mapping
    const timezoneOffsets = {
        "America/New_York": 300,    // UTC-5
        "America/Chicago": 360,     // UTC-6
        "America/Denver": 420,      // UTC-7
        "America/Los_Angeles": 480, // UTC-8
        "America/Adak": 600         // UTC-10
    };

    const LOCATION_DATA = {
        timezone: selectedTimezone,
        timezone_name: selectedTimezone.split('/')[1].replace('_', ' ') + ' Time',
        latitude: parseFloat((selectedCity.lat + latOffset).toFixed(6)),
        longitude: parseFloat((selectedCity.lng + lngOffset).toFixed(6)),
        city: selectedCity.city,
        state: selectedCity.state,
        country: "United States",
        country_code: "US",
        accuracy: accuracyVariation,
        offset: -(timezoneOffsets[selectedTimezone] / 60), // 转换为小时
        dst: true
    };

    // 创建时区显示格式化函数
    function formatTimezoneString(timezone, utcTime, offsetHours) {
        const localTime = new OriginalDate(utcTime - (offsetHours * 60 * 60 * 1000));
        const year = localTime.getFullYear();
        const month = String(localTime.getMonth() + 1).padStart(2, '0');
        const day = String(localTime.getDate()).padStart(2, '0');
        const hours = String(localTime.getHours()).padStart(2, '0');
        const minutes = String(localTime.getMinutes()).padStart(2, '0');
        const seconds = String(localTime.getSeconds()).padStart(2, '0');

        const timeStr = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        return `${timezone}(GMT)Local time: ${timeStr}`;
    }

    // 1. 强化时区覆盖
    const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
    Intl.DateTimeFormat.prototype.resolvedOptions = function() {
        const options = originalResolvedOptions.call(this);
        options.timeZone = LOCATION_DATA.timezone;
        options.locale = 'en-US';
        return options;
    };

    // 强制覆盖所有时区偏移方法 - 使用动态时区偏移
    const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
    const dynamicOffset = timezoneOffsets[selectedTimezone];
    Date.prototype.getTimezoneOffset = function() {
        return dynamicOffset; // 根据选择的时区返回对应偏移
    };

    // 覆盖Date构造函数和相关方法
    const OriginalDate = Date;
    const originalToString = Date.prototype.toString;
    const originalToDateString = Date.prototype.toDateString;
    const originalToTimeString = Date.prototype.toTimeString;
    const originalToLocaleString = Date.prototype.toLocaleString;

    // 重写toString方法以显示正确的美国时区时间
    Date.prototype.toString = function() {
        const utcTime = this.getTime();
        const offsetHours = dynamicOffset / 60; // 转换为小时
        const localTime = new OriginalDate(utcTime - (offsetHours * 60 * 60 * 1000));

        // 使用新的时区格式化函数
        const timezoneString = formatTimezoneString(selectedTimezone, utcTime, offsetHours);

        const str = originalToString.call(localTime);
        return str.replace(/GMT[+-]\d{4}.*$/, timezoneString);
    };

    // 重写toDateString方法
    Date.prototype.toDateString = function() {
        const utcTime = this.getTime();
        const offsetHours = dynamicOffset / 60;
        const localTime = new OriginalDate(utcTime - (offsetHours * 60 * 60 * 1000));
        return originalToDateString.call(localTime);
    };

    // 重写toTimeString方法
    Date.prototype.toTimeString = function() {
        const utcTime = this.getTime();
        const offsetHours = dynamicOffset / 60;
        const localTime = new OriginalDate(utcTime - (offsetHours * 60 * 60 * 1000));

        // 使用新的时区格式化函数
        const timezoneString = formatTimezoneString(selectedTimezone, utcTime, offsetHours);

        const str = originalToTimeString.call(localTime);
        return str.replace(/GMT[+-]\d{4}.*$/, timezoneString);
    };

    // 重写toLocaleString以使用美国时区
    Date.prototype.toLocaleString = function(locales, options) {
        options = options || {};
        options.timeZone = LOCATION_DATA.timezone;

        // 强制使用美国格式和时区
        const result = originalToLocaleString.call(this, 'en-US', options);
        console.log('🕐 toLocaleString called:', result, 'TimeZone:', options.timeZone);
        return result;
    };

    // 完全覆盖Date构造函数以确保时区一致性
    const OriginalDateConstructor = window.Date;

    window.Date = function(...args) {
        let date;
        if (args.length === 0) {
            // new Date() - 当前时间, 需要调整到美国时区
            const utcNow = new OriginalDateConstructor().getTime();
            const offsetHours = dynamicOffset / 60;
            const usTime = utcNow - (offsetHours * 60 * 60 * 1000);
            date = new OriginalDateConstructor(usTime);
        } else {
            // 有参数的构造函数
            date = new OriginalDateConstructor(...args);
        }

        // 强制覆盖所有时区相关方法
        date.getTimezoneOffset = function() {
            return dynamicOffset;
        };

        date.toString = Date.prototype.toString;
        date.toDateString = Date.prototype.toDateString;
        date.toTimeString = Date.prototype.toTimeString;
        date.toLocaleString = Date.prototype.toLocaleString;
        date.toLocaleDateString = Date.prototype.toLocaleDateString;
        date.toLocaleTimeString = Date.prototype.toLocaleTimeString;

        // 覆盖getHours, getMinutes, getSeconds等方法
        const originalGetHours = date.getHours;
        const originalGetMinutes = date.getMinutes;
        const originalGetSeconds = date.getSeconds;

        date.getHours = function() {
            const utcTime = this.getTime();
            const offsetHours = dynamicOffset / 60;
            const localTime = new OriginalDateConstructor(utcTime - (offsetHours * 60 * 60 * 1000));
            return localTime.getHours();
        };

        date.getMinutes = function() {
            const utcTime = this.getTime();
            const offsetHours = dynamicOffset / 60;
            const localTime = new OriginalDateConstructor(utcTime - (offsetHours * 60 * 60 * 1000));
            return localTime.getMinutes();
        };

        date.getSeconds = function() {
            const utcTime = this.getTime();
            const offsetHours = dynamicOffset / 60;
            const localTime = new OriginalDateConstructor(utcTime - (offsetHours * 60 * 60 * 1000));
            return localTime.getSeconds();
        };

        return date;
    };

    // 保持Date的静态方法和属性
    Object.setPrototypeOf(window.Date, OriginalDateConstructor);
    Object.setPrototypeOf(window.Date.prototype, OriginalDateConstructor.prototype);

    // 覆盖Date.now()返回美国时间戳
    window.Date.now = function() {
        const utcNow = OriginalDateConstructor.now();
        const offsetHours = dynamicOffset / 60;
        return utcNow - (offsetHours * 60 * 60 * 1000);
    };

    window.Date.parse = OriginalDateConstructor.parse;
    window.Date.UTC = OriginalDateConstructor.UTC;

    // 2. 强化Intl.DateTimeFormat覆盖
    const OriginalDateTimeFormat = Intl.DateTimeFormat;

    // 完全重写Intl.DateTimeFormat构造函数
    Intl.DateTimeFormat = function(locales, options) {
        // 强制使用美国东部时区
        options = options || {};
        options.timeZone = LOCATION_DATA.timezone;
        locales = locales || 'en-US';

        // 调用原始构造函数并返回
        return new OriginalDateTimeFormat(locales, options);
    };

    // 保持原型链和静态方法
    Intl.DateTimeFormat.prototype = OriginalDateTimeFormat.prototype;
    Intl.DateTimeFormat.supportedLocalesOf = OriginalDateTimeFormat.supportedLocalesOf;

    // 3. 覆盖其他时区相关方法

    // 覆盖toLocaleDateString
    const originalToLocaleDateString = Date.prototype.toLocaleDateString;
    Date.prototype.toLocaleDateString = function(locales, options) {
        options = options || {};
        options.timeZone = LOCATION_DATA.timezone;
        return originalToLocaleDateString.call(this, 'en-US', options);
    };

    // 覆盖toLocaleTimeString
    const originalToLocaleTimeString = Date.prototype.toLocaleTimeString;
    Date.prototype.toLocaleTimeString = function(locales, options) {
        options = options || {};
        options.timeZone = LOCATION_DATA.timezone;
        return originalToLocaleTimeString.call(this, 'en-US', options);
    };

    // 4. 覆盖Temporal API (如果存在)
    if (typeof Temporal !== 'undefined' && Temporal.Now) {
        const originalTimeZone = Temporal.Now.timeZone;
        Temporal.Now.timeZone = function() {
            return Temporal.TimeZone.from(LOCATION_DATA.timezone);
        };
    }

    // 5. 强化地理位置覆盖 - 完全替换geolocation对象

    // 生成固定的坐标偏移, 避免坐标跳动
    const fixedLatOffset = (Math.random() - 0.5) * 0.01; // 固定的纬度偏移
    const fixedLngOffset = (Math.random() - 0.5) * 0.01; // 固定的经度偏移
    const FIXED_LAT = parseFloat((LOCATION_DATA.latitude + fixedLatOffset).toFixed(6));
    const FIXED_LNG = parseFloat((LOCATION_DATA.longitude + fixedLngOffset).toFixed(6));

    console.log('🎯 Fixed coordinates generated:', FIXED_LAT, FIXED_LNG, '(no jumping)');

    // 创建一个新的geolocation对象
    const fakeGeolocation = {
        getCurrentPosition: function(success, error, options) {
            if (success) {
                // 使用固定坐标，不再每次生成新的
                const finalLat = FIXED_LAT;
                const finalLng = FIXED_LNG;

                const position = {
                    coords: {
                        latitude: finalLat,
                        longitude: finalLng,
                        accuracy: LOCATION_DATA.accuracy + Math.floor(Math.random() * 20) - 10, // ±10米变化
                        altitude: null,
                        altitudeAccuracy: null,
                        heading: null,
                        speed: null
                    },
                    timestamp: Date.now()
                };
                console.log('🎯 Returning US coordinates:', finalLat, finalLng, 'City:', LOCATION_DATA.city);
                setTimeout(() => success(position), 100);
            }
        },

        watchPosition: function(success, error, options) {
            if (success) {
                // 使用相同的固定坐标，确保一致性
                const position = {
                    coords: {
                        latitude: FIXED_LAT,
                        longitude: FIXED_LNG,
                        accuracy: LOCATION_DATA.accuracy + Math.floor(Math.random() * 20) - 10,
                        altitude: null,
                        altitudeAccuracy: null,
                        heading: null,
                        speed: null
                    },
                    timestamp: Date.now()
                };
                setTimeout(() => success(position), 100);
            }
            return Math.floor(Math.random() * 1000) + 1; // 返回随机watchId
        },

        clearWatch: function(watchId) {
            // 空实现
        }
    };

    // Completely replace navigator.geolocation - multiple overrides to ensure effectiveness
    try {
        // Method 1: Skip direct assignment (navigator.geolocation is read-only)
        console.log('🔧 Skipping direct assignment method (navigator.geolocation is read-only)');

        // Method 2: defineProperty override (most reliable method)
        try {
            // Try to delete existing property if possible
            try {
                delete navigator.geolocation;
            } catch (deleteError) {
                // Deletion failure is normal, continue execution
            }

            Object.defineProperty(navigator, 'geolocation', {
                get: function() {
                    console.log('🎯 Geolocation API called, returning US coordinates:', FIXED_LAT, FIXED_LNG);
                    return fakeGeolocation;
                },
                set: function(value) {
                    // Prevent external reset
                    console.log('🛡️ Blocking external geolocation reset');
                },
                configurable: true,
                enumerable: true
            });
            console.log('✅ Method 2: defineProperty override successful');
        } catch (defineError) {
            console.warn('⚠️ Method 2 failed:', defineError.message);
        }

        // Method 3: Force override prototype methods
        try {
            if (navigator.geolocation && typeof navigator.geolocation === 'object') {
                navigator.geolocation.getCurrentPosition = fakeGeolocation.getCurrentPosition;
                navigator.geolocation.watchPosition = fakeGeolocation.watchPosition;
                navigator.geolocation.clearWatch = fakeGeolocation.clearWatch;
                console.log('✅ Method 3: Prototype method override successful');
            }
        } catch (protoError) {
            console.warn('⚠️ Method 3 failed:', protoError.message);
        }

        // Method 4: Global interception (most powerful method)
        try {
            // Intercept possible Geolocation constructor
            if (window.Geolocation && window.Geolocation.prototype) {
                const originalGetCurrentPosition = window.Geolocation.prototype.getCurrentPosition;
                const originalWatchPosition = window.Geolocation.prototype.watchPosition;

                window.Geolocation.prototype.getCurrentPosition = fakeGeolocation.getCurrentPosition;
                window.Geolocation.prototype.watchPosition = fakeGeolocation.watchPosition;
                console.log('✅ Method 4: Global Geolocation prototype override successful');
            }
        } catch (globalError) {
            console.warn('⚠️ Method 4 failed:', globalError.message);
        }

        console.log('✅ Geolocation override applied, current coordinates:', LOCATION_DATA.latitude, LOCATION_DATA.longitude);

        // Immediately test if geolocation is effective
        setTimeout(() => {
            try {
                if (navigator.geolocation && navigator.geolocation.getCurrentPosition) {
                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            console.log('🧪 Geolocation test result:', position.coords.latitude, position.coords.longitude);
                            if (position.coords.latitude === 35.664000 || position.coords.longitude === 139.698200) {
                                console.error('❌ Warning: Geolocation override failed, still showing Japanese coordinates!');
                            } else {
                                console.log('✅ Geolocation override successful, showing US coordinates');
                            }
                        },
                        (error) => {
                            console.log('🧪 Geolocation test error:', error.message);
                        }
                    );
                }
            } catch (testError) {
                console.warn('⚠️ Geolocation test failed:', testError.message);
            }
        }, 1000);
    } catch (e) {
        console.error('❌ Geolocation override critical failure:', e.message, e.stack);
    }

    // 6. 增强语言和区域设置
    const languageVariations = [
        ['en-US', 'en'],
        ['en-US', 'en', 'es'],
        ['en-US', 'en', 'fr'],
        ['en-US']
    ];
    const selectedLanguages = languageVariations[Math.floor(Math.random() * languageVariations.length)];

    // 安全地覆盖语言设置
    try {
        Object.defineProperty(navigator, 'language', {
            get: function() { return selectedLanguages[0]; },
            configurable: true
        });
    } catch (e) {
        console.warn('⚠️ Cannot override navigator.language:', e.message);
    }

    try {
        Object.defineProperty(navigator, 'languages', {
            get: function() { return selectedLanguages; },
            configurable: true
        });
    } catch (e) {
        console.warn('⚠️ Cannot override navigator.languages:', e.message);
    }

    // 7. 随机化其他指纹信息

    // 随机化屏幕分辨率（常见美国用户分辨率）
    const commonResolutions = [
        {width: 1920, height: 1080},
        {width: 1366, height: 768},
        {width: 1440, height: 900},
        {width: 1536, height: 864},
        {width: 1280, height: 720},
        {width: 2560, height: 1440},
        {width: 1600, height: 900}
    ];
    const selectedResolution = commonResolutions[Math.floor(Math.random() * commonResolutions.length)];

    // 覆盖屏幕信息
    Object.defineProperty(screen, 'width', {
        get: function() { return selectedResolution.width; }
    });
    Object.defineProperty(screen, 'height', {
        get: function() { return selectedResolution.height; }
    });
    Object.defineProperty(screen, 'availWidth', {
        get: function() { return selectedResolution.width; }
    });
    Object.defineProperty(screen, 'availHeight', {
        get: function() { return selectedResolution.height - 40; } // 减去任务栏高度
    });

    // 安全地随机化硬件并发数（CPU核心数）
    const cpuCores = [4, 6, 8, 12, 16][Math.floor(Math.random() * 5)];
    try {
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: function() { return cpuCores; },
            configurable: true
        });
    } catch (e) {
        console.warn('⚠️ Cannot override navigator.hardwareConcurrency:', e.message);
    }

    // 安全地随机化设备内存（如果支持）
    if ('deviceMemory' in navigator) {
        const memoryOptions = [4, 8, 16, 32];
        const selectedMemory = memoryOptions[Math.floor(Math.random() * memoryOptions.length)];
        try {
            Object.defineProperty(navigator, 'deviceMemory', {
                get: function() { return selectedMemory; },
                configurable: true
            });
        } catch (e) {
            console.warn('⚠️ Cannot override navigator.deviceMemory:', e.message);
        }
    }

    // 安全地随机化连接信息（如果支持）
    if ('connection' in navigator) {
        const connectionTypes = ['4g', 'wifi', 'ethernet'];
        const selectedConnection = connectionTypes[Math.floor(Math.random() * connectionTypes.length)];
        try {
            Object.defineProperty(navigator.connection, 'effectiveType', {
                get: function() { return selectedConnection; },
                configurable: true
            });
        } catch (e) {
            console.warn('⚠️ Cannot override navigator.connection.effectiveType:', e.message);
        }
    }

    // 8. 控制台输出 - 显示随机化信息
    console.log('🇺🇸 US Timezone & Location Spoofer Enhanced v2.7.2 Active');
    console.log('📍 Fixed Location: New York, NY (纽约) - 不再随机');
    console.log('🕐 Fixed Timezone: America/New_York (东部时间) - 不再随机');
    console.log('📊 Base Coordinates:', LOCATION_DATA.latitude + ', ' + LOCATION_DATA.longitude);
    console.log('⏰ Timezone Offset:', dynamicOffset + ' minutes (UTC' + (dynamicOffset > 0 ? '+' : '') + (-dynamicOffset/60) + ')');
    console.log('🖥️ Resolution:', selectedResolution.width + 'x' + selectedResolution.height);
    console.log('💻 CPU Cores:', cpuCores);
    console.log('🗣️ Languages:', selectedLanguages.join(', '));
    console.log('🎯 Accuracy Variation:', LOCATION_DATA.accuracy + '±10 meters');
    console.log('🔧 Fixed coordinates prevent location jumping');
    console.log('🕐 Current time test:', new Date().toString());
    console.log('👨‍💻 Author: XiaoYuYouShui | 🌐 Website: https://xoxome.online');
    console.log('🌍 Current website:', currentHost, '- Sensitive site:', isSensitiveHost);

    // 检测可能的反欺诈测试
    const detectAntifraudTests = () => {
        // 检测隐藏的测试元素
        const hiddenElements = document.querySelectorAll('[style*="display:none"], [style*="visibility:hidden"], .hidden');
        hiddenElements.forEach(el => {
            if (el.textContent.includes('timezone') || el.textContent.includes('location') || el.textContent.includes('country')) {
                console.log('🕵️ Detected potential antifraud test element:', el);
                // 确保隐藏元素也显示美国信息
                if (el.textContent.includes('timezone')) {
                    el.textContent = el.textContent.replace(/timezone[^a-zA-Z]*[a-zA-Z_\/]+/gi, `timezone: ${LOCATION_DATA.timezone}`);
                }
                if (el.textContent.includes('country')) {
                    el.textContent = el.textContent.replace(/country[^a-zA-Z]*[a-zA-Z]+/gi, 'country: US');
                }
            }
        });

        // 检测可能的JavaScript测试
        const scripts = document.querySelectorAll('script');
        scripts.forEach(script => {
            if (script.textContent.includes('getTimezoneOffset') || script.textContent.includes('Intl.DateTimeFormat')) {
                console.log('🕵️ Detected script with timezone detection:', script.src || 'inline');
            }
        });
    };

    // 延迟执行检测，确保页面加载完成
    setTimeout(detectAntifraudTests, 1000);
    setTimeout(detectAntifraudTests, 3000); // 再次检测动态加载的内容

    // 添加调试信息输出
    setTimeout(() => {
        console.log('🔍 === 插件状态检查 ===');
        console.log('🌍 地理位置:', LOCATION_DATA.city, LOCATION_DATA.state);
        console.log('🕐 时区:', LOCATION_DATA.timezone);
        console.log('📍 坐标:', FIXED_LAT, FIXED_LNG);
        console.log('🌐 用户代理:', navigator.userAgent);
        console.log('⏰ 时区偏移:', new Date().getTimezoneOffset());
        console.log('🗓️ 本地时间:', new Date().toLocaleString());
        console.log('🌏 Intl时区:', Intl.DateTimeFormat().resolvedOptions().timeZone);

        // 测试地理位置API
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (pos) => console.log('📍 地理位置测试:', pos.coords.latitude, pos.coords.longitude),
                (err) => console.log('❌ 地理位置错误:', err.message)
            );
        }
    }, 2000);

    // 13. 拦截定时器确保实时时间更新使用美国时间（敏感网站跳过）
    if (!isSensitiveHost) {
        const originalSetInterval = window.setInterval;
        const originalSetTimeout = window.setTimeout;

        window.setInterval = function(callback, delay, ...args) {
            if (typeof callback === 'function') {
                const wrappedCallback = function() {
                    // 在回调执行前确保Date对象正确
                    return callback.apply(this, arguments);
                };
                return originalSetInterval.call(this, wrappedCallback, delay, ...args);
            }
            return originalSetInterval.call(this, callback, delay, ...args);
        };

        window.setTimeout = function(callback, delay, ...args) {
            if (typeof callback === 'function') {
                const wrappedCallback = function() {
                    // 在回调执行前确保Date对象正确
                    return callback.apply(this, arguments);
                };
                return originalSetTimeout.call(this, wrappedCallback, delay, ...args);
            }
            return originalSetTimeout.call(this, callback, delay, ...args);
        };
    } else {
        console.log('🛡️ Skipping timer interception to protect website functionality');
    }

    // 覆盖Performance.now()以保持时间一致性
    if (typeof performance !== 'undefined' && performance.now) {
        const originalPerformanceNow = performance.now;
        performance.now = function() {
            const result = originalPerformanceNow.call(this);
            // 保持性能计时的相对准确性，不做时区调整
            return result;
        };
    }

    // 覆盖requestAnimationFrame中的时间戳
    const originalRequestAnimationFrame = window.requestAnimationFrame;
    window.requestAnimationFrame = function(callback) {
        const wrappedCallback = function(timestamp) {
            // 确保动画帧回调中的时间戳使用美国时间基准
            return callback.call(this, timestamp);
        };
        return originalRequestAnimationFrame.call(this, wrappedCallback);
    };

    // 9. 地理位置相关API全面覆盖

    // 注意：Date对象的本地化方法已在前面定义，这里不重复定义

    // 覆盖Number和其他对象的本地化方法
    const originalNumberToLocaleString = Number.prototype.toLocaleString;
    Number.prototype.toLocaleString = function(locales, options) {
        return originalNumberToLocaleString.call(this, 'en-US', options);
    };

    // 覆盖Intl.NumberFormat
    const OriginalNumberFormat = Intl.NumberFormat;
    Intl.NumberFormat = function(locales, options) {
        return new OriginalNumberFormat('en-US', options);
    };
    Intl.NumberFormat.prototype = OriginalNumberFormat.prototype;
    Intl.NumberFormat.supportedLocalesOf = OriginalNumberFormat.supportedLocalesOf;

    // 覆盖Intl.Collator
    const OriginalCollator = Intl.Collator;
    Intl.Collator = function(locales, options) {
        return new OriginalCollator('en-US', options);
    };
    Intl.Collator.prototype = OriginalCollator.prototype;
    Intl.Collator.supportedLocalesOf = OriginalCollator.supportedLocalesOf;

    // 覆盖其他可能暴露地区的Intl对象
    if (typeof Intl.RelativeTimeFormat !== 'undefined') {
        const OriginalRelativeTimeFormat = Intl.RelativeTimeFormat;
        Intl.RelativeTimeFormat = function(locales, options) {
            return new OriginalRelativeTimeFormat('en-US', options);
        };
        Intl.RelativeTimeFormat.prototype = OriginalRelativeTimeFormat.prototype;
        Intl.RelativeTimeFormat.supportedLocalesOf = OriginalRelativeTimeFormat.supportedLocalesOf;
    }

    if (typeof Intl.PluralRules !== 'undefined') {
        const OriginalPluralRules = Intl.PluralRules;
        Intl.PluralRules = function(locales, options) {
            return new OriginalPluralRules('en-US', options);
        };
        Intl.PluralRules.prototype = OriginalPluralRules.prototype;
        Intl.PluralRules.supportedLocalesOf = OriginalPluralRules.supportedLocalesOf;
    }

    // 覆盖可能通过网络请求暴露位置的API（敏感网站跳过）
    if (!isSensitiveHost) {
        const originalFetch = window.fetch;
        window.fetch = function(input, init) {
            const url = typeof input === 'string' ? input : input.url;

            // 特殊处理Verisoul反欺诈服务 - 简化处理避免干扰其他请求
            if (url && url.includes('verisoul.ai')) {
                console.log('🛡️ Intercepting Verisoul anti-fraud request:', url);
                // 直接返回成功响应，避免复杂的修改逻辑
                return Promise.resolve(new Response(JSON.stringify({
                    success: true,
                    status: 'verified',
                    country: 'US',
                    region: LOCATION_DATA.state,
                    city: LOCATION_DATA.city,
                    timezone: LOCATION_DATA.timezone,
                    risk_score: 0.1,
                    verification_status: 'passed',
                    device_trust: 'high',
                    location_trust: 'verified'
                }), {
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                        'Access-Control-Allow-Headers': 'Content-Type'
                    }
                }));
            }

            // 拦截IP位置检测服务 - 扩展检测范围
            const locationDetectionPatterns = [
                'ipapi.co', 'ip-api.com', 'ipinfo.io', 'geoip', 'ipgeolocation',
                'freegeoip', 'cloudflare', 'maxmind', 'location', 'geo',
                'country', 'region', 'whatismyip', 'myip', 'checkip', 'getip',
                'ipecho', 'icanhazip', 'ident.me', 'ifconfig.me', 'httpbin.org/ip'
            ];

            if (url && locationDetectionPatterns.some(pattern => url.includes(pattern))) {
                console.log('🚫 Intercepting IP/location detection request:', url);
                // 返回美国位置的模拟响应
                return Promise.resolve(new Response(JSON.stringify({
                    country: 'United States',
                    country_code: 'US',
                    country_name: 'United States',
                    region: LOCATION_DATA.state,
                    region_code: LOCATION_DATA.state.substring(0, 2).toUpperCase(),
                    city: LOCATION_DATA.city,
                    latitude: FIXED_LAT,
                    longitude: FIXED_LNG,
                    timezone: LOCATION_DATA.timezone,
                    timezone_name: LOCATION_DATA.timezone_name,
                    ip: '*******', // 使用Google DNS作为示例IP
                    status: 'success',
                    message: 'success'
                }), {
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    }
                }));
            }

            // 检查请求头，移除可能暴露位置的信息
            if (init && init.headers) {
                const headers = new Headers(init.headers);
                // 移除可能的地理位置相关头部和可能导致CORS问题的自定义头部
                headers.delete('CF-IPCountry');
                headers.delete('X-Forwarded-For');
                headers.delete('X-Real-IP');
                headers.delete('X-Country-Code');
                headers.delete('CF-Connecting-IP');
                headers.delete('X-Geo-Country');
                // 只设置标准的、不会触发CORS预检的头部
                headers.set('Accept-Language', 'en-US,en;q=0.9');
                init.headers = headers;
            } else if (init) {
                init.headers = {
                    'Accept-Language': 'en-US,en;q=0.9'
                };
            } else {
                init = {
                    headers: {
                        'Accept-Language': 'en-US,en;q=0.9'
                    }
                };
            }
            return originalFetch.call(this, input, init);
        };

        // 覆盖XMLHttpRequest - 更精确的拦截，避免干扰正常API
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;

        XMLHttpRequest.prototype.open = function(method, url) {
            const result = originalXHROpen.apply(this, arguments);

            // 只对特定的地理位置相关请求设置语言头
            if (url && (url.includes('geo') || url.includes('location') || url.includes('ip') || url.includes('country'))) {
                try {
                    this.setRequestHeader('Accept-Language', 'en-US,en;q=0.9');
                } catch (e) {
                    // 忽略设置头部失败的情况
                }
            }
            return result;
        };

        XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
            // 只拦截语言相关的头部，其他保持原样
            if (name.toLowerCase() === 'accept-language') {
                value = 'en-US,en;q=0.9';
            }
            return originalXHRSetRequestHeader.call(this, name, value);
        };
    } else {
        console.log('🛡️ Skipping network request interception to protect website functionality');
    }

    // 10. 高级反检测措施

    // 随机化WebGL指纹
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (gl) {
        const originalGetParameter = gl.getParameter;
        gl.getParameter = function(parameter) {
            // 随机化GPU厂商和渲染器信息
            if (parameter === gl.VENDOR) {
                const vendors = ['Intel Inc.', 'NVIDIA Corporation', 'AMD', 'Apple Inc.'];
                return vendors[Math.floor(Math.random() * vendors.length)];
            }
            if (parameter === gl.RENDERER) {
                const renderers = [
                    'Intel Iris OpenGL Engine',
                    'NVIDIA GeForce GTX 1060',
                    'AMD Radeon Pro 560',
                    'Apple M1',
                    'Intel HD Graphics 620'
                ];
                return renderers[Math.floor(Math.random() * renderers.length)];
            }
            return originalGetParameter.call(this, parameter);
        };
    }

    // 一致化Canvas指纹 - 模拟美国用户的典型硬件配置
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function() {
        // 提供一致的Canvas指纹，避免随机性被检测
        const context = this.getContext('2d');
        if (context) {
            const imageData = context.getImageData(0, 0, this.width, this.height);
            // 使用固定的模式来模拟美国用户的典型GPU配置
            for (let i = 0; i < imageData.data.length; i += 4) {
                if (i % 1000 === 0) { // 固定模式，不使用随机
                    imageData.data[i] = Math.min(255, imageData.data[i] + 1);
                }
            }
            context.putImageData(imageData, 0, 0);
        }
        return originalToDataURL.apply(this, arguments);
    };

    // 随机化AudioContext指纹
    if (typeof AudioContext !== 'undefined') {
        const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
        AudioContext.prototype.createAnalyser = function() {
            const analyser = originalCreateAnalyser.call(this);
            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
            analyser.getFloatFrequencyData = function(array) {
                originalGetFloatFrequencyData.call(this, array);
                // 添加微小的随机变化
                for (let i = 0; i < array.length; i++) {
                    array[i] += (Math.random() - 0.5) * 0.0001;
                }
            };
            return analyser;
        };
    }

    // 随机化字体检测
    const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
    const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');

    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
        get: function() {
            const width = originalOffsetWidth.get.call(this);
            // 对字体测量元素添加微小变化
            if (this.style && this.style.fontFamily) {
                return width + (Math.random() < 0.5 ? 0 : 1);
            }
            return width;
        }
    });

    Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
        get: function() {
            const height = originalOffsetHeight.get.call(this);
            // 对字体测量元素添加微小变化
            if (this.style && this.style.fontFamily) {
                return height + (Math.random() < 0.5 ? 0 : 1);
            }
            return height;
        }
    });

    // 11. 覆盖可能通过DNS/网络暴露位置的API

    // 覆盖WebRTC相关API，防止IP泄露
    const originalRTCPeerConnection = window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection;
    if (originalRTCPeerConnection) {
        const originalCreateOffer = originalRTCPeerConnection.prototype.createOffer;
        const originalCreateAnswer = originalRTCPeerConnection.prototype.createAnswer;
        const originalSetLocalDescription = originalRTCPeerConnection.prototype.setLocalDescription;

        originalRTCPeerConnection.prototype.createOffer = function(options) {
            // 禁用可能暴露真实IP的选项
            const modifiedOptions = {
                ...options,
                offerToReceiveAudio: false,
                offerToReceiveVideo: false
            };
            return originalCreateOffer.call(this, modifiedOptions);
        };

        originalRTCPeerConnection.prototype.createAnswer = function(options) {
            const modifiedOptions = {
                ...options,
                offerToReceiveAudio: false,
                offerToReceiveVideo: false
            };
            return originalCreateAnswer.call(this, modifiedOptions);
        };
    }

    // 覆盖Performance API中可能暴露位置的信息
    if (typeof performance !== 'undefined' && performance.getEntriesByType) {
        const originalGetEntriesByType = performance.getEntriesByType;
        performance.getEntriesByType = function(type) {
            const entries = originalGetEntriesByType.call(this, type);
            // 过滤可能包含地理信息的条目
            return entries.filter(entry => {
                if (entry.name) {
                    const name = entry.name.toLowerCase();
                    return !name.includes('geoip') &&
                           !name.includes('location') &&
                           !name.includes('country') &&
                           !name.includes('region');
                }
                return true;
            });
        };
    }

    // 覆盖可能通过CSS媒体查询暴露位置的API
    if (typeof window.matchMedia !== 'undefined') {
        const originalMatchMedia = window.matchMedia;
        window.matchMedia = function(query) {
            // 拦截可能的地理位置相关媒体查询
            if (query.includes('prefers-color-scheme')) {
                // 强制使用美国常见的浅色主题偏好
                return {
                    matches: query.includes('light'),
                    media: query,
                    addListener: function() {},
                    removeListener: function() {},
                    addEventListener: function() {},
                    removeEventListener: function() {},
                    dispatchEvent: function() { return true; }
                };
            }
            return originalMatchMedia.call(this, query);
        };
    }

    // 安全地覆盖可能暴露系统语言的API
    try {
        Object.defineProperty(navigator, 'systemLanguage', {
            get: function() { return 'en-US'; },
            configurable: true
        });
    } catch (e) {
        console.warn('⚠️ Cannot override navigator.systemLanguage:', e.message);
    }

    try {
        Object.defineProperty(navigator, 'userLanguage', {
            get: function() { return 'en-US'; },
            configurable: true
        });
    } catch (e) {
        console.warn('⚠️ Cannot override navigator.userLanguage:', e.message);
    }

    try {
        Object.defineProperty(navigator, 'browserLanguage', {
            get: function() { return 'en-US'; },
            configurable: true
        });
    } catch (e) {
        console.warn('⚠️ Cannot override navigator.browserLanguage:', e.message);
    }

    // 安全地覆盖可能暴露地区的平台信息
    try {
        Object.defineProperty(navigator, 'platform', {
            get: function() {
                const platforms = ['Win32', 'MacIntel', 'Linux x86_64'];
                return platforms[Math.floor(Math.random() * platforms.length)];
            },
            configurable: true
        });
    } catch (e) {
        console.warn('⚠️ Cannot override navigator.platform:', e.message);
    }

    // 覆盖用户代理和其他可能暴露地区的navigator属性
    try {
        Object.defineProperty(navigator, 'userAgent', {
            get: function() {
                return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
            },
            configurable: true
        });

        Object.defineProperty(navigator, 'appVersion', {
            get: function() {
                return '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
            },
            configurable: true
        });

        Object.defineProperty(navigator, 'vendor', {
            get: function() { return 'Google Inc.'; },
            configurable: true
        });

        console.log('✅ User agent and vendor information overridden');
        console.log('🔍 Current User Agent:', navigator.userAgent);
        console.log('🔍 Current App Version:', navigator.appVersion);
        console.log('🔍 Current Vendor:', navigator.vendor);
    } catch (e) {
        console.warn('⚠️ Cannot override navigator userAgent/vendor:', e.message);
    }

    // 注意：getTimezoneOffset已在前面覆盖，这里不重复定义

    // 覆盖可能通过Cookie暴露位置的信息
    const originalCookieGetter = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie').get;
    const originalCookieSetter = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie').set;

    Object.defineProperty(document, 'cookie', {
        get: function() {
            let cookies = originalCookieGetter.call(this);
            // 过滤可能包含地理信息的cookie
            cookies = cookies.replace(/[^;]*(?:country|region|location|timezone|geoip)[^;]*;?\s*/gi, '');
            return cookies;
        },
        set: function(value) {
            // 拦截设置地理位置相关的cookie
            if (!/(?:country|region|location|timezone|geoip)/i.test(value)) {
                return originalCookieSetter.call(this, value);
            }
        }
    });

    // 12. 增强反检测 - 覆盖可能暴露真实位置的高级API

    // 覆盖RTCPeerConnection以防止WebRTC IP泄露
    if (window.RTCPeerConnection) {
        const originalRTCPeerConnection = window.RTCPeerConnection;
        window.RTCPeerConnection = function(config) {
            if (config && config.iceServers) {
                // 强制使用美国的STUN服务器
                config.iceServers = [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ];
            }
            console.log('🔒 WebRTC connection intercepted, using US STUN servers');
            return new originalRTCPeerConnection(config);
        };
    }

    // 覆盖可能通过DNS查询暴露位置的API
    if (navigator.connection) {
        try {
            Object.defineProperty(navigator.connection, 'effectiveType', {
                get: function() { return '4g'; },
                configurable: true
            });
            Object.defineProperty(navigator.connection, 'downlink', {
                get: function() { return 10; }, // 10 Mbps - 美国平均网速
                configurable: true
            });
            Object.defineProperty(navigator.connection, 'rtt', {
                get: function() { return 50; }, // 50ms - 美国平均延迟
                configurable: true
            });
        } catch (e) {
            console.warn('⚠️ Cannot override navigator.connection properties:', e.message);
        }
    }

    // 覆盖可能通过时间戳分析暴露位置的Performance API
    if (window.performance && window.performance.now) {
        const originalPerformanceNow = window.performance.now;
        const startTime = Date.now();
        window.performance.now = function() {
            // 确保性能时间戳与美国时区一致
            const usTime = new Date().getTime();
            return usTime - startTime;
        };
    }

    // 覆盖可能暴露系统时区的Intl API
    if (window.Intl && window.Intl.DateTimeFormat) {
        const originalResolvedOptions = window.Intl.DateTimeFormat.prototype.resolvedOptions;
        window.Intl.DateTimeFormat.prototype.resolvedOptions = function() {
            const options = originalResolvedOptions.call(this);
            // 强制返回美国时区信息
            options.timeZone = LOCATION_DATA.timezone;
            options.locale = 'en-US';
            return options;
        };
    }

    // 12. 最终防护措施

    // 覆盖可能通过localStorage/sessionStorage暴露位置的数据
    const originalLocalStorageGetItem = localStorage.getItem;
    const originalLocalStorageSetItem = localStorage.setItem;
    const originalSessionStorageGetItem = sessionStorage.getItem;
    const originalSessionStorageSetItem = sessionStorage.setItem;

    localStorage.getItem = function(key) {
        const value = originalLocalStorageGetItem.call(this, key);
        // 如果是地理位置相关的数据，返回美国数据
        if (key && /(?:country|region|location|timezone|geoip|lat|lng|city)/i.test(key)) {
            if (key.toLowerCase().includes('country')) return 'US';
            if (key.toLowerCase().includes('timezone')) return LOCATION_DATA.timezone;
            if (key.toLowerCase().includes('city')) return LOCATION_DATA.city;
            if (key.toLowerCase().includes('region')) return LOCATION_DATA.state;
        }
        return value;
    };

    localStorage.setItem = function(key, value) {
        // 拦截设置地理位置数据，替换为美国数据
        if (key && /(?:country|region|location|timezone|geoip)/i.test(key)) {
            if (key.toLowerCase().includes('country')) value = 'US';
            if (key.toLowerCase().includes('timezone')) value = LOCATION_DATA.timezone;
            if (key.toLowerCase().includes('city')) value = LOCATION_DATA.city;
            if (key.toLowerCase().includes('region')) value = LOCATION_DATA.state;
        }
        return originalLocalStorageSetItem.call(this, key, value);
    };

    sessionStorage.getItem = function(key) {
        const value = originalSessionStorageGetItem.call(this, key);
        if (key && /(?:country|region|location|timezone|geoip|lat|lng|city)/i.test(key)) {
            if (key.toLowerCase().includes('country')) return 'US';
            if (key.toLowerCase().includes('timezone')) return LOCATION_DATA.timezone;
            if (key.toLowerCase().includes('city')) return LOCATION_DATA.city;
            if (key.toLowerCase().includes('region')) return LOCATION_DATA.state;
        }
        return value;
    };

    sessionStorage.setItem = function(key, value) {
        if (key && /(?:country|region|location|timezone|geoip)/i.test(key)) {
            if (key.toLowerCase().includes('country')) value = 'US';
            if (key.toLowerCase().includes('timezone')) value = LOCATION_DATA.timezone;
            if (key.toLowerCase().includes('city')) value = LOCATION_DATA.city;
            if (key.toLowerCase().includes('region')) value = LOCATION_DATA.state;
        }
        return originalSessionStorageSetItem.call(this, key, value);
    };

    // 覆盖可能通过URL参数暴露位置的信息
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(state, title, url) {
        // 清理URL中可能的地理位置参数
        if (url && typeof url === 'string') {
            url = url.replace(/[?&](?:country|region|location|timezone|geoip|lat|lng)=[^&]*/gi, '');
        }
        return originalPushState.call(this, state, title, url);
    };

    history.replaceState = function(state, title, url) {
        if (url && typeof url === 'string') {
            url = url.replace(/[?&](?:country|region|location|timezone|geoip|lat|lng)=[^&]*/gi, '');
        }
        return originalReplaceState.call(this, state, title, url);
    };

    // 覆盖可能暴露真实IP的WebSocket连接
    const originalWebSocket = window.WebSocket;
    if (originalWebSocket) {
        window.WebSocket = function(url, protocols) {
            // 在WebSocket连接中添加美国地区标识
            const ws = new originalWebSocket(url, protocols);

            const originalSend = ws.send;
            ws.send = function(data) {
                // 如果数据包含地理位置信息，替换为美国信息
                if (typeof data === 'string') {
                    data = data.replace(/"country":\s*"[^"]*"/gi, '"country":"US"');
                    data = data.replace(/"timezone":\s*"[^"]*"/gi, `"timezone":"${LOCATION_DATA.timezone}"`);
                    data = data.replace(/"region":\s*"[^"]*"/gi, `"region":"${LOCATION_DATA.state}"`);
                }
                return originalSend.call(this, data);
            };

            return ws;
        };
        window.WebSocket.prototype = originalWebSocket.prototype;
    }

})();

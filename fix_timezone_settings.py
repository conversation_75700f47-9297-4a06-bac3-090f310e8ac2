#!/usr/bin/env python3
"""
修复时区设置，完善反中国地区检测
"""
import json
import os

def fix_timezone_settings():
    """修复浏览器时区设置"""
    print("🕐 开始修复时区设置...")
    
    chrome_base = "134/chrome-win64"
    chrome_data_paths = [
        f"{chrome_base}/chrome-debug-data/Default",
        f"{chrome_base}/chrome-debug-profile/Default"
    ]
    
    # 推荐的非中国时区
    recommended_timezones = [
        "America/New_York",      # 美国东部时间
        "America/Los_Angeles",   # 美国西部时间
        "Europe/London",         # 英国时间
        "Europe/Berlin",         # 德国时间
        "Australia/Sydney",      # 澳大利亚时间
        "America/Toronto"        # 加拿大时间
    ]
    
    # 选择美国东部时间作为默认
    default_timezone = "America/New_York"
    
    for chrome_data_path in chrome_data_paths:
        if not os.path.exists(chrome_data_path):
            continue
            
        preferences_file = f"{chrome_data_path}/Preferences"
        if os.path.exists(preferences_file):
            try:
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    prefs = json.load(f)
                
                # 设置时区相关配置
                if 'profile' not in prefs:
                    prefs['profile'] = {}
                
                # 强制设置时区
                prefs['profile']['timezone'] = default_timezone
                
                # 设置地理位置相关
                if 'profile' not in prefs:
                    prefs['profile'] = {}
                if 'default_content_setting_values' not in prefs['profile']:
                    prefs['profile']['default_content_setting_values'] = {}
                
                # 禁用地理位置API
                prefs['profile']['default_content_setting_values']['geolocation'] = 2
                
                # 设置更多反检测配置
                if 'browser' not in prefs:
                    prefs['browser'] = {}
                
                # 设置搜索引擎为Google美国版
                prefs['browser']['default_search_provider_data'] = {
                    "template_url_data": {
                        "url": "https://www.google.com/search?q={searchTerms}&gl=us&hl=en",
                        "short_name": "Google",
                        "keyword": "google.com"
                    }
                }
                
                # 禁用翻译功能（避免暴露中文）
                if 'translate' not in prefs:
                    prefs['translate'] = {}
                prefs['translate']['enabled'] = False
                
                # 设置DNS over HTTPS为美国服务器
                if 'dns_over_https' not in prefs:
                    prefs['dns_over_https'] = {}
                prefs['dns_over_https']['mode'] = "secure"
                prefs['dns_over_https']['templates'] = "https://dns.google/dns-query"
                
                # 写回配置文件
                with open(preferences_file, 'w', encoding='utf-8') as f:
                    json.dump(prefs, f, separators=(',', ':'))
                
                print(f"✅ 修复时区设置: {preferences_file}")
                print(f"   🕐 设置时区为: {default_timezone}")
                
            except Exception as e:
                print(f"❌ 修复时区设置失败: {e}")

def create_vytal_config():
    """创建Vytal插件推荐配置"""
    print("\n📋 创建Vytal插件推荐配置...")
    
    vytal_config = {
        "timezone": "America/New_York",
        "locale": "en-US",
        "geolocation": {
            "latitude": 40.7128,   # 纽约坐标
            "longitude": -74.0060,
            "accuracy": 100
        },
        "webrtc": {
            "enabled": False  # 禁用WebRTC防止IP泄露
        },
        "screen": {
            "width": 1920,
            "height": 1080,
            "colorDepth": 24
        },
        "language": "en-US",
        "userAgent": "auto"  # 让插件自动选择
    }
    
    # 保存配置到文件
    config_file = "vytal_recommended_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(vytal_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 已创建Vytal配置文件: {config_file}")
    
    return vytal_config

def print_usage_instructions():
    """打印使用说明"""
    print("\n" + "="*60)
    print("📖 使用说明和建议")
    print("="*60)
    
    print("\n🔧 1. Vytal插件配置步骤:")
    print("   1) 启动浏览器后，点击工具栏中的Vytal插件图标")
    print("   2) 设置时区为: America/New_York (美国东部时间)")
    print("   3) 设置地理位置为: 纽约 (40.7128, -74.0060)")
    print("   4) 设置语言为: en-US")
    print("   5) 启用 'Spoof timezone' 和 'Spoof geolocation'")
    print("   6) 点击 'Apply' 应用设置")
    
    print("\n🛡️ 2. 额外安全建议:")
    print("   • 定期更换时区和地理位置")
    print("   • 避免访问中国网站或使用中文搜索")
    print("   • 考虑使用VPN服务进一步隐藏真实IP")
    print("   • 定期清理浏览器缓存和Cookie")
    
    print("\n🔍 3. 验证设置:")
    print("   • 访问 https://whatismyipaddress.com/ 检查地理位置")
    print("   • 访问 https://www.timeanddate.com/ 检查时区")
    print("   • 访问 https://browserleaks.com/ 进行全面检测")
    
    print("\n⚠️ 4. 注意事项:")
    print("   • 每次启动浏览器后都要检查Vytal插件状态")
    print("   • 如果插件失效，重新配置设置")
    print("   • 避免在同一会话中混用不同地区的设置")

def main():
    """主函数"""
    print("🔧 开始修复浏览器时区和地区设置")
    print("="*50)
    
    # 1. 修复浏览器配置文件中的时区设置
    fix_timezone_settings()
    
    # 2. 创建Vytal插件推荐配置
    vytal_config = create_vytal_config()
    
    # 3. 打印使用说明
    print_usage_instructions()
    
    print("\n🎉 设置修复完成！")
    print("\n📝 总结:")
    print("   ✅ 已移除timezone插件")
    print("   ✅ 已安装Vytal (ncb.crx) 插件")
    print("   ✅ 已设置浏览器语言为英语")
    print("   ✅ 已修复浏览器配置文件中的时区设置")
    print("   ✅ 已创建Vytal插件推荐配置")
    
    print("\n🚀 下一步:")
    print("   1. 启动浏览器")
    print("   2. 配置Vytal插件（按照上述说明）")
    print("   3. 运行 python quick_china_check.py 验证设置")

if __name__ == "__main__":
    main()

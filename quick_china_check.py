#!/usr/bin/env python3
"""
快速检查浏览器中国地区暴露情况
"""
import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

def quick_china_check():
    """快速检查浏览器是否暴露中国地区信息"""
    
    print("🔍 快速检查浏览器中国地区暴露情况...")
    
    # 检查Chrome路径
    chrome_path = os.path.join(os.getcwd(), "134", "chrome-win64", "chrome.exe")
    if not os.path.exists(chrome_path):
        print(f"❌ Chrome路径不存在: {chrome_path}")
        return False
    
    # 检查插件路径
    available_extensions = []
    
    ncb_plugin_path = os.path.join(os.getcwd(), "ncb")
    if os.path.exists(ncb_plugin_path):
        available_extensions.append(ncb_plugin_path)
        print(f"✅ 发现ncb插件: {ncb_plugin_path}")
    
    finger_plugin_path = os.path.join(os.getcwd(), "finger")
    if os.path.exists(finger_plugin_path):
        available_extensions.append(finger_plugin_path)
        print(f"✅ 发现finger插件: {finger_plugin_path}")
    
    # 创建临时测试配置目录
    test_profile_path = os.path.join(os.getcwd(), "quick_test_profile")
    os.makedirs(test_profile_path, exist_ok=True)
    
    try:
        # 配置Chrome选项
        options = Options()
        options.binary_location = chrome_path
        options.add_argument(f"--user-data-dir={test_profile_path}")
        options.add_argument("--remote-debugging-port=9224")
        options.add_argument("--no-first-run")
        options.add_argument("--no-default-browser-check")
        options.add_argument("--disable-background-timer-throttling")
        options.add_argument("--disable-renderer-backgrounding")
        options.add_argument("--disable-backgrounding-occluded-windows")
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=VizDisplayCompositor")
        options.add_argument("--log-level=3")
        options.add_argument("--silent")
        
        # 强制英语语言设置
        options.add_argument("--lang=en-US")
        options.add_experimental_option('prefs', {
            'intl.accept_languages': 'en-US,en',
            'intl.selected_languages': ['en-US', 'en']
        })
        
        # 加载插件
        if available_extensions:
            extension_paths = ",".join(available_extensions)
            options.add_argument(f"--load-extension={extension_paths}")
            options.add_argument("--enable-extensions")
            print(f"🔧 加载插件: {len(available_extensions)} 个")
        
        # 启动浏览器
        print("🚀 启动Chrome浏览器...")
        driver = webdriver.Chrome(options=options)
        
        # 等待浏览器启动
        time.sleep(3)
        
        print("\n🔍 检测核心指标...")
        
        # 检查基本浏览器信息
        basic_info = driver.execute_script("""
            return {
                language: navigator.language,
                languages: navigator.languages,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                locale: Intl.DateTimeFormat().resolvedOptions().locale,
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                timezoneOffset: new Date().getTimezoneOffset()
            };
        """)
        
        china_issues = []
        
        print("\n📊 检测结果:")
        
        # 1. 检查语言设置
        if 'zh' in basic_info['language'].lower() or 'cn' in basic_info['language'].lower():
            china_issues.append(f"语言设置暴露中国: {basic_info['language']}")
            print(f"❌ 语言设置: {basic_info['language']}")
        else:
            print(f"✅ 语言设置: {basic_info['language']}")
        
        # 2. 检查语言列表
        china_langs = [lang for lang in basic_info['languages'] if 'zh' in lang.lower() or 'cn' in lang.lower()]
        if china_langs:
            china_issues.append(f"语言列表包含中文: {china_langs}")
            print(f"❌ 语言列表: {basic_info['languages']}")
        else:
            print(f"✅ 语言列表: {basic_info['languages']}")
        
        # 3. 检查时区
        if 'shanghai' in basic_info['timezone'].lower() or 'china' in basic_info['timezone'].lower() or 'asia/shanghai' in basic_info['timezone'].lower():
            china_issues.append(f"时区暴露中国: {basic_info['timezone']}")
            print(f"❌ 时区设置: {basic_info['timezone']}")
        else:
            print(f"✅ 时区设置: {basic_info['timezone']}")
        
        # 4. 检查地区设置
        if 'cn' in basic_info['locale'].lower() or 'zh' in basic_info['locale'].lower():
            china_issues.append(f"地区设置暴露中国: {basic_info['locale']}")
            print(f"❌ 地区设置: {basic_info['locale']}")
        else:
            print(f"✅ 地区设置: {basic_info['locale']}")
        
        # 5. 检查时区偏移（中国是UTC+8，即-480分钟）
        if basic_info['timezoneOffset'] == -480:
            china_issues.append(f"时区偏移暴露中国时区: {basic_info['timezoneOffset']} 分钟")
            print(f"❌ 时区偏移: {basic_info['timezoneOffset']} 分钟 (中国时区)")
        else:
            print(f"✅ 时区偏移: {basic_info['timezoneOffset']} 分钟")
        
        print(f"✅ User-Agent: {basic_info['userAgent'][:60]}...")
        print(f"✅ 平台信息: {basic_info['platform']}")
        
        # 检查插件状态
        print("\n🔌 检查插件状态:")
        try:
            driver.get("chrome://extensions/")
            time.sleep(2)
            
            page_source = driver.page_source.lower()
            
            if "vytal" in page_source or "spoof timezone" in page_source:
                print("✅ Vytal (ncb) 插件已加载")
            else:
                print("⚠️ Vytal (ncb) 插件可能未正确加载")
            
            if "canvas" in page_source or "fingerprint" in page_source:
                print("✅ Canvas Fingerprint Defender 插件已加载")
            else:
                print("⚠️ Canvas Fingerprint Defender 插件可能未正确加载")
                
        except Exception as e:
            print(f"⚠️ 检查插件状态失败: {e}")
        
        # 总结
        print("\n" + "="*50)
        print("📋 检测总结:")
        
        if china_issues:
            print(f"❌ 发现 {len(china_issues)} 个中国地区暴露问题:")
            for issue in china_issues:
                print(f"   • {issue}")
            
            print("\n💡 解决建议:")
            print("   1. 使用Vytal插件手动设置时区和地理位置")
            print("   2. 检查浏览器语言设置")
            print("   3. 重启浏览器让设置生效")
            return False
        else:
            print("✅ 未发现明显的中国地区暴露问题！")
            print("🎉 浏览器伪装设置良好")
            return True
        
    except Exception as e:
        print(f"❌ 检测过程中发生错误: {e}")
        return False
        
    finally:
        try:
            driver.quit()
            print("\n🔚 浏览器已关闭")
        except:
            pass
        
        # 清理测试配置目录
        try:
            import shutil
            shutil.rmtree(test_profile_path, ignore_errors=True)
            print("🧹 清理完成")
        except:
            pass

if __name__ == "__main__":
    print("🔧 开始快速中国地区暴露检测")
    print("="*50)
    
    success = quick_china_check()
    
    if success:
        print("\n🎉 检测完成：浏览器伪装良好！")
        print("💡 建议：定期检查Vytal插件设置，确保时区和地理位置正确")
    else:
        print("\n⚠️ 检测完成：发现需要改进的地方")
        print("💡 建议：打开Vytal插件进行手动配置")

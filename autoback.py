"""
自动后端处理中心
接收外部请求并自动执行AddmemClick
"""
import os
import sys
import signal
import atexit
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import sqlite3
import json
from datetime import datetime, timezone, timedelta
from flask import Flask, request, jsonify, make_response
# from flask_cors import CORS  # 不再使用，改用自定义CORS处理
import mysql.connector
from mysql.connector import Error
from auth_manager import AuthManager
from config_manager import ConfigManager
from browser_manager import BrowserManager
from automation_engine import AutomationEngine
from profile_usage_tracker import ProfileUsageTracker
from sqlite_pool import get_sqlite_connection, get_pooled_connection, cleanup_all_pools
# 公告功能已迁移到前端本地文件，不再需要静态公告
# from static_announcements import get_all_announcements

class MainUIWithAPI:
    """集成API服务器的主UI"""

    def load_config(self):
        """加载配置文件"""
        try:
            # 检查是否为混合模式（生产服务器运行时）
            hybrid_mode = self.check_production_server_running()

            # 获取环境变量，默认为development
            env = os.getenv('NODE_ENV', 'development')

            # 如果检测到生产服务器运行，使用混合模式配置
            if hybrid_mode:
                env = 'hybrid'
                print("🔄 检测到生产服务器运行，启用混合模式")

            # 读取配置文件
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 根据模式选择配置
            if env == 'hybrid' and 'hybrid' in config_data:
                # 混合模式：返回管理界面配置，但包装成原有的结构
                mgmt_config = config_data['hybrid']['management_gui']
                return {
                    'api': {
                        'host': mgmt_config.get('host', '127.0.0.1'),
                        'port': mgmt_config.get('port', 5002),
                        'base_url': mgmt_config.get('base_url', '/api')
                    }
                }
            else:
                # 标准模式：返回原有配置结构
                return config_data.get(env, config_data['development'])

        except Exception as e:
            print(f"加载配置文件失败: {e}")
            # 返回默认配置（保持原有结构）
            return {
                "api": {
                    "host": "127.0.0.1",  # 管理界面只监听本地
                    "port": 5002,  # 使用5002端口避免冲突
                    "base_url": "/api"
                }
            }

    def check_production_server_running(self):
        """检查生产服务器是否运行"""
        try:
            import requests
            response = requests.get('http://localhost:5001/api/test', timeout=2)
            return response.status_code == 200
        except:
            return False

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 自动后端处理中心")
        self.root.geometry("900x700")

        # 加载配置文件
        self.config = self.load_config()

        # 北京时区
        self.beijing_tz = timezone(timedelta(hours=8))

        # MySQL数据库配置
        self.mysql_config = {
            'host': 'localhost',
            'database': 'activeaug',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4',
            'autocommit': True,
            'connection_timeout': 15,  # 增加到15秒连接超时
            'pool_reset_session': True,
            'use_unicode': True,
            'raise_on_warnings': False  # 降低警告级别
        }

        # 激活码数据库配置
        self.jihuoma_config = {
            'host': '**************',
            'database': 'jihuoma',
            'user': 'jihuoma',
            'password': '123123',
            'charset': 'utf8mb4',
            'autocommit': True,
            'connection_timeout': 15,  # 增加到15秒连接超时
            'pool_reset_session': True,
            'use_unicode': True
        }

        # 初始化认证管理器
        self.auth_manager = AuthManager(
            main_db_config=self.mysql_config,
            jihuoma_db_config=self.jihuoma_config,
            jwt_secret="augment-system-secret-key-2025"
        )

        # 初始化组件
        self.config_manager = ConfigManager()
        self.browser_manager = BrowserManager(self.config_manager)
        self.automation_engine = AutomationEngine(self.browser_manager)
        self.usage_tracker = ProfileUsageTracker()

        # 任务超时配置
        self.task_timeout_seconds = 300  # 5分钟超时
        self.task_start_time = None
        self.timeout_check_enabled = True

        # 初始化数据库
        self.init_database()

        # 公告功能已迁移到前端本地文件，不再需要后端缓存
        # self.load_announcements_from_database()

        # 初始化Flask应用
        self.init_flask_app()

        # 状态变量
        self.is_processing = False
        self.current_task = None
        self.api_server_running = False
        self.current_profile_name = None
        self.start_time = time.time()  # 记录启动时间

        # 公告功能已迁移到前端本地文件，移除缓存相关变量
        # self.cached_announcements = []
        # self.announcements_cache_time = 0
        # self.announcements_cache_duration = 300  # 5分钟缓存

        # 程序关闭状态控制
        self.is_closing = False
        self.cleanup_completed = False



        # 创建界面
        self.create_widgets()

        # 启动API服务器
        self.start_api_server()

        # 启动任务处理器
        self.start_task_processor()

        # 启动超时检查器
        self.start_timeout_checker()

    def init_database(self):
        """初始化数据库"""
        with get_sqlite_connection('tasks.db') as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS task_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_token VARCHAR(100) NOT NULL,
                    user_id INTEGER NULL,
                    email VARCHAR(255) NOT NULL,
                    status VARCHAR(20) DEFAULT 'waiting',
                    queue_position INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    started_at TIMESTAMP NULL,
                    completed_at TIMESTAMP NULL,
                    result_message TEXT NULL,
                    processing_details TEXT NULL
                )
            ''')

            # 添加user_id列（如果不存在）
            try:
                conn.execute('ALTER TABLE task_queue ADD COLUMN user_id INTEGER NULL')
            except sqlite3.OperationalError:
                # 列已存在，忽略错误
                pass

            conn.execute('''
                CREATE TABLE IF NOT EXISTS system_status (
                    id INTEGER PRIMARY KEY DEFAULT 1,
                    current_processing_task_id INTEGER NULL,
                    current_email VARCHAR(255) NULL,
                    is_processing BOOLEAN DEFAULT FALSE,
                    started_at TIMESTAMP NULL,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 插入默认系统状态
            conn.execute('''
                INSERT OR IGNORE INTO system_status (id, is_processing)
                VALUES (1, FALSE)
            ''')

    def init_flask_app(self):
        """初始化Flask应用"""
        self.app = Flask(__name__)

        # 自定义CORS处理 - 支持tunnel域名
        @self.app.before_request
        def handle_preflight():
            if request.method == "OPTIONS":
                response = make_response()
                # 支持tunnel域名和本地开发
                origin = request.headers.get('Origin')
                allowed_origins = [
                    'https://aug8.xyz',
                    'http://localhost:3000',
                    'http://127.0.0.1:3000',
                    'http://localhost:80',
                    'http://127.0.0.1:80'
                ]

                if origin in allowed_origins:
                    response.headers["Access-Control-Allow-Origin"] = origin
                else:
                    response.headers["Access-Control-Allow-Origin"] = "*"

                response.headers["Access-Control-Allow-Methods"] = "GET,POST,PUT,DELETE,OPTIONS,HEAD"
                response.headers["Access-Control-Allow-Headers"] = "Content-Type,Authorization,X-Requested-With,Accept,Origin"
                response.headers["Access-Control-Allow-Credentials"] = "true"
                response.headers["Access-Control-Max-Age"] = "86400"
                return response

        @self.app.after_request
        def after_request(response):
            # 支持tunnel域名和本地开发 - 优化Cloudflare Tunnel支持
            origin = request.headers.get('Origin')
            allowed_origins = [
                'https://aug8.xyz',
                'http://aug8.xyz',  # 添加HTTP支持
                'http://localhost:3000',
                'http://127.0.0.1:3000',
                'http://localhost:80',
                'http://127.0.0.1:80'
            ]

            if origin in allowed_origins:
                response.headers["Access-Control-Allow-Origin"] = origin
            else:
                response.headers["Access-Control-Allow-Origin"] = "*"

            response.headers["Access-Control-Allow-Methods"] = "GET,POST,PUT,DELETE,OPTIONS,HEAD"
            response.headers["Access-Control-Allow-Headers"] = "Content-Type,Authorization,X-Requested-With,Accept,Origin,CF-Ray,CF-Connecting-IP"
            response.headers["Access-Control-Allow-Credentials"] = "true"

            # 添加Cloudflare Tunnel优化头 - 学习learn版本的稳定配置
            response.headers["X-Content-Type-Options"] = "nosniff"
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"

            # 设置连接保持活跃，减少context canceled错误 - 学习learn版本
            response.headers["Connection"] = "keep-alive"
            response.headers["Keep-Alive"] = "timeout=30, max=100"

            # 设置连接保持活跃，减少context canceled错误
            response.headers["Connection"] = "keep-alive"
            response.headers["Keep-Alive"] = "timeout=30, max=100"

            return response

        # 🔥 任务提交路由 - 接收 production_server.py 转发的任务
        @self.app.route('/api/submit', methods=['POST'])
        def submit_task():
            return self.handle_submit_task()

        # 🔥 保留任务处理相关路由 - autoback.py 专门负责任务处理
        @self.app.route('/api/task/<int:task_id>/status', methods=['GET'])
        def get_task_status(task_id):
            return self.handle_get_task_status(task_id)

        @self.app.route('/api/system/status', methods=['GET'])
        def get_system_status():
            return self.handle_get_system_status()

        @self.app.route('/api/queue/list', methods=['GET'])
        def get_queue_list():
            return self.handle_get_queue_list()

        @self.app.route('/api/test', methods=['GET', 'POST'])
        def test_api():
            if request.method == 'POST':
                data = request.get_json() or {}
                return jsonify({
                    'success': True,
                    'message': 'POST request received!',
                    'data': data,
                    'timestamp': time.time(),
                    'tunnel_compatible': True
                })
            else:
                return jsonify({
                    'success': True,
                    'message': 'API is working!',
                    'timestamp': time.time(),
                    'tunnel_compatible': True
                })

        # 公告API已废弃，前端现在直接读取本地JSON文件
        # @self.app.route('/api/announcements', methods=['GET'])
        # def get_announcements():
        #     return self.handle_get_announcements()



        # 公告管理路由已废弃，前端现在直接读取本地JSON文件
        # @self.app.route('/api/admin/refresh-announcements', methods=['POST'])
        # def refresh_announcements():
        #     return self.handle_refresh_announcements()

        # 🔥 移除用户认证相关路由 - 现在由 production_server.py 专门处理
        # @self.app.route('/api/auth/check', methods=['POST'])
        # def auth_check():
        #     return self.handle_auth_check()
        # @self.app.route('/api/auth/register', methods=['POST'])
        # def register():
        #     return self.handle_register()

        # @self.app.route('/api/auth/login', methods=['POST'])
        # def login():
        #     return self.handle_login()

        # @self.app.route('/api/auth/profile', methods=['GET'])
        # def get_profile():
        #     return self.handle_get_profile()

        # @self.app.route('/api/recharge/activate', methods=['POST'])
        # def activate_code():
        #     return self.handle_activate_code()

        # @self.app.route('/api/config/recharge-url', methods=['GET'])
        # def get_recharge_url():
        #     return self.handle_get_recharge_url()

        # 🔥 移除用户提交历史相关路由 - 现在由 production_server.py 专门处理
        # @self.app.route('/api/user/submissions', methods=['GET'])
        # def get_user_submissions():
        #     return self.handle_get_user_submissions()

        # 内部API：接收生产服务器的任务通知
        @self.app.route('/api/internal/task-notification', methods=['POST'])
        def task_notification():
            return self.handle_task_notification()

        # 内部API：接收生产服务器的任务提交
        @self.app.route('/api/internal/submit-task', methods=['POST'])
        def internal_submit_task():
            return self.handle_internal_submit_task()

    def create_widgets(self):
        """创建界面组件"""

        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=10, pady=5)

        title_label = ttk.Label(title_frame, text="🎯 自动后端处理中心",
                               font=("Arial", 16, "bold"))
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text="接收外部请求并自动执行AddmemClick",
                                  font=("Arial", 10))
        subtitle_label.pack()

        # 创建主内容区域（左右分割）
        main_content_frame = ttk.Frame(self.root)
        main_content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 左侧框架（配置管理和日志）
        left_frame = ttk.Frame(main_content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 右侧框架（任务队列）
        right_frame = ttk.Frame(main_content_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 配置选择区域（左侧）
        config_frame = ttk.LabelFrame(left_frame, text="📋 配置管理", padding=10)
        config_frame.pack(fill=tk.X, pady=(0, 5))

        profile_info_frame = ttk.Frame(config_frame)
        profile_info_frame.pack(fill=tk.X)

        ttk.Label(profile_info_frame, text="当前可用配置:").pack(side=tk.LEFT)

        self.profile_var = tk.StringVar()
        self.profile_combo = ttk.Combobox(profile_info_frame, textvariable=self.profile_var,
                                         state="readonly", width=25)
        self.profile_combo.pack(side=tk.LEFT, padx=5)

        # 配置统计显示
        self.profile_stats_var = tk.StringVar()
        ttk.Label(profile_info_frame, textvariable=self.profile_stats_var,
                 font=("Arial", 9)).pack(side=tk.LEFT, padx=10)

        # 配置操作按钮
        ttk.Button(profile_info_frame, text="🌐 打开配置",
                  command=self.open_current_profile_browser).pack(side=tk.LEFT, padx=5)

        ttk.Button(profile_info_frame, text="🚀 启动main.py",
                  command=self.launch_main_py).pack(side=tk.LEFT, padx=5)

        # 加载配置文件
        self.load_profiles()

        # API服务器状态
        api_frame = ttk.LabelFrame(self.root, text="🌐 API服务器", padding=10)
        api_frame.pack(fill=tk.X, padx=10, pady=5)

        api_status_frame = ttk.Frame(api_frame)
        api_status_frame.pack(fill=tk.X)

        ttk.Label(api_status_frame, text="服务器状态:").pack(side=tk.LEFT)
        self.api_status_var = tk.StringVar(value="🔴 未启动")
        ttk.Label(api_status_frame, textvariable=self.api_status_var,
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=10)

        ttk.Label(api_status_frame, text="地址: http://localhost:5000").pack(side=tk.LEFT, padx=20)

        # 系统状态显示
        status_frame = ttk.LabelFrame(self.root, text="📊 处理状态", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)

        # 当前处理状态
        current_frame = ttk.Frame(status_frame)
        current_frame.pack(fill=tk.X)

        ttk.Label(current_frame, text="当前状态:").pack(side=tk.LEFT)
        self.current_status_var = tk.StringVar(value="🟢 空闲中")
        ttk.Label(current_frame, textvariable=self.current_status_var,
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=10)

        # 队列统计
        queue_frame = ttk.Frame(status_frame)
        queue_frame.pack(fill=tk.X, pady=2)

        ttk.Label(queue_frame, text="队列统计:").pack(side=tk.LEFT)
        self.queue_stats_var = tk.StringVar(value="等待: 0, 完成: 0, 失败: 0")
        ttk.Label(queue_frame, textvariable=self.queue_stats_var).pack(side=tk.LEFT, padx=10)

        # 超时配置
        timeout_frame = ttk.Frame(status_frame)
        timeout_frame.pack(fill=tk.X, pady=2)

        ttk.Label(timeout_frame, text="任务超时:").pack(side=tk.LEFT)
        self.timeout_var = tk.StringVar(value=str(self.task_timeout_seconds))
        timeout_spinbox = ttk.Spinbox(timeout_frame, from_=60, to=1800, width=8,
                                     textvariable=self.timeout_var,
                                     command=self.update_timeout_setting)
        timeout_spinbox.pack(side=tk.LEFT, padx=5)
        ttk.Label(timeout_frame, text="秒").pack(side=tk.LEFT)

        # 绑定回车键更新超时设置
        timeout_spinbox.bind('<Return>', lambda e: self.update_timeout_setting())

        # 超时状态显示
        self.timeout_status_var = tk.StringVar(value="⏰ 超时检查已启用")
        ttk.Label(timeout_frame, textvariable=self.timeout_status_var,
                 font=("Arial", 9)).pack(side=tk.LEFT, padx=10)

        # 操作日志（左侧）
        log_frame = ttk.LabelFrame(left_frame, text="📝 操作日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=60)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 控制按钮（左侧底部）
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=5)

        # 任务队列显示（右侧）
        self.create_task_queue_panel(right_frame)

        ttk.Button(control_frame, text="🔄 刷新配置",
                  command=self.load_profiles).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="📊 刷新队列",
                  command=self.refresh_queue_display).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="📊 查看Excel",
                  command=self.open_excel_file).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="🔄 重置配置",
                  command=self.reset_all_profiles).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="🖥️ 打开前端",
                  command=self.open_frontend).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="📋 清空任务历史",
                  command=self.clear_task_history).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="🗑️ 清空日志",
                  command=self.clear_log).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="❌ 退出",
                  command=self.on_closing).pack(side=tk.RIGHT, padx=5)

        # 启动状态更新
        self.start_status_update()

    def update_timeout_setting(self):
        """更新超时设置"""
        try:
            new_timeout = int(self.timeout_var.get())
            if 60 <= new_timeout <= 1800:  # 1分钟到30分钟
                old_timeout = self.task_timeout_seconds
                self.task_timeout_seconds = new_timeout
                self.log(f"⏰ 任务超时时间已更新: {old_timeout}秒 → {new_timeout}秒")
                self.timeout_status_var.set(f"⏰ 超时检查已启用 ({new_timeout}秒)")
            else:
                self.log(f"⚠️ 超时时间无效，必须在60-1800秒之间")
                self.timeout_var.set(str(self.task_timeout_seconds))  # 恢复原值
        except ValueError:
            self.log(f"⚠️ 超时时间格式错误")
            self.timeout_var.set(str(self.task_timeout_seconds))  # 恢复原值

    def get_beijing_time(self):
        """获取北京时间字符串"""
        return datetime.now(self.beijing_tz).strftime('%Y-%m-%d %H:%M:%S')

    def format_beijing_time(self, time_str):
        """将时间字符串格式化为北京时间显示"""
        if not time_str:
            return ''
        try:
            # 如果是UTC时间，转换为北京时间
            if time_str.endswith('Z') or '+' in time_str:
                dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
                beijing_dt = dt.astimezone(self.beijing_tz)
            else:
                # 假设是本地时间，直接解析
                dt = datetime.fromisoformat(time_str)
                beijing_dt = dt.replace(tzinfo=self.beijing_tz)

            return beijing_dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return time_str

    def get_mysql_connection(self):
        """获取MySQL数据库连接"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            return connection
        except Error as e:
            self.log(f"❌ MySQL连接失败: {e}")
            return None

    # 公告功能已迁移到前端本地文件，不再需要从数据库获取
    # def get_announcements_from_db(self):
    #     """从MySQL数据库获取公告数据"""
    #     try:
    #         connection = self.get_mysql_connection()
    #         if not connection:
    #             return []
    #
    #         cursor = connection.cursor(dictionary=True)
    #         query = """
    #             SELECT id, title, content, type, is_active, sort_order,
    #                    created_at, updated_at
    #             FROM announcements
    #             WHERE is_active = 1
    #             ORDER BY sort_order ASC, created_at DESC
    #         """
    #         cursor.execute(query)
    #         results = cursor.fetchall()
    #
    #         # 转换datetime对象为字符串
    #         for result in results:
    #             if result['created_at']:
    #                 result['created_at'] = result['created_at'].strftime('%Y-%m-%d %H:%M:%S')
    #             if result['updated_at']:
    #                 result['updated_at'] = result['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
    #
    #         cursor.close()
    #         connection.close()
    #
    #         self.log(f"📢 成功获取 {len(results)} 条公告数据")
    #         return results
    #
    #     except Error as e:
    #         self.log(f"❌ 获取公告数据失败: {e}")
    #         return []
    #     except Exception as e:
    #         self.log(f"❌ 获取公告数据异常: {e}")
    #         return []

    def create_task_queue_panel(self, parent):
        """创建任务队列面板（右侧）"""
        # 任务队列主框架
        queue_main_frame = ttk.LabelFrame(parent, text="📋 任务队列管理", padding=10)
        queue_main_frame.pack(fill=tk.BOTH, expand=True)

        # 任务分类选择
        filter_frame = ttk.Frame(queue_main_frame)
        filter_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(filter_frame, text="显示类型:").pack(side=tk.LEFT)

        self.task_filter_var = tk.StringVar(value="all")
        filter_options = [
            ("所有任务", "all"),
            ("待处理", "pending"),
            ("已处理", "completed")
        ]

        for text, value in filter_options:
            ttk.Radiobutton(filter_frame, text=text, variable=self.task_filter_var,
                           value=value, command=self.refresh_task_display).pack(side=tk.LEFT, padx=10)

        # 任务统计信息
        stats_frame = ttk.Frame(queue_main_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        self.task_stats_var = tk.StringVar(value="总计: 0 | 等待: 0 | 处理中: 0 | 成功: 0 | 失败: 0")
        ttk.Label(stats_frame, textvariable=self.task_stats_var, font=("Arial", 9)).pack()

        # 添加刷新提示信息
        self.refresh_hint_var = tk.StringVar(value="💡 已切换为手动刷新模式，按F5或点击刷新按钮更新数据")
        hint_label = ttk.Label(stats_frame, textvariable=self.refresh_hint_var,
                              font=("Arial", 8), foreground="gray")
        hint_label.pack(pady=(2,0))

        # 任务列表
        list_frame = ttk.Frame(queue_main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建任务列表
        columns = ('ID', '邮箱', '状态', '提交时间', '结果')
        self.tasks_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # 设置列标题和宽度
        self.tasks_tree.heading('ID', text='ID')
        self.tasks_tree.heading('邮箱', text='邮箱地址')
        self.tasks_tree.heading('状态', text='状态')
        self.tasks_tree.heading('提交时间', text='提交时间')
        self.tasks_tree.heading('结果', text='处理结果')

        self.tasks_tree.column('ID', width=50, anchor='center')
        self.tasks_tree.column('邮箱', width=180)
        self.tasks_tree.column('状态', width=80, anchor='center')
        self.tasks_tree.column('提交时间', width=120, anchor='center')
        self.tasks_tree.column('结果', width=200)

        # 添加滚动条
        tasks_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tasks_tree.yview)
        self.tasks_tree.configure(yscrollcommand=tasks_scrollbar.set)

        self.tasks_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tasks_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 配置表格颜色标记
        self.tasks_tree.tag_configure('completed', background='#d4edda', foreground='#155724')  # 绿色 - 成功
        self.tasks_tree.tag_configure('failed', background='#f8d7da', foreground='#721c24')     # 红色 - 失败
        self.tasks_tree.tag_configure('processing', background='#d1ecf1', foreground='#0c5460') # 蓝色 - 处理中
        self.tasks_tree.tag_configure('pending', background='#fff3cd', foreground='#856404')    # 黄色 - 等待

        # 绑定右键菜单和双击事件
        self.tasks_tree.bind("<Button-3>", self.show_task_context_menu)  # 右键
        self.tasks_tree.bind("<Double-1>", self.show_task_details)  # 双击

        # 任务操作按钮
        task_buttons_frame = ttk.Frame(queue_main_frame)
        task_buttons_frame.pack(fill=tk.X, pady=(10, 0))

        refresh_btn = ttk.Button(task_buttons_frame, text="🔄 刷新任务 (F5)",
                  command=self.refresh_task_display)
        refresh_btn.pack(side=tk.LEFT, padx=5)

        # 绑定F5快捷键
        self.root.bind('<F5>', lambda e: self.refresh_task_display())

        # 添加自动刷新开关
        auto_refresh_frame = ttk.Frame(task_buttons_frame)
        auto_refresh_frame.pack(side=tk.RIGHT, padx=5)

        self.auto_refresh_var = tk.BooleanVar(value=False)  # 默认关闭自动刷新
        auto_refresh_cb = ttk.Checkbutton(auto_refresh_frame,
                                         text="自动刷新",
                                         variable=self.auto_refresh_var,
                                         command=self._toggle_auto_refresh)
        auto_refresh_cb.pack(side=tk.LEFT)

        # 添加刷新间隔设置
        ttk.Label(auto_refresh_frame, text="间隔:").pack(side=tk.LEFT, padx=(10,2))
        self.refresh_interval_var = tk.StringVar(value="10")
        interval_combo = ttk.Combobox(auto_refresh_frame,
                                     textvariable=self.refresh_interval_var,
                                     values=["5", "10", "15", "30", "60"],
                                     width=5, state="readonly")
        interval_combo.pack(side=tk.LEFT, padx=(0,2))
        ttk.Label(auto_refresh_frame, text="秒").pack(side=tk.LEFT)

        ttk.Button(task_buttons_frame, text="🗑️ 删除选中",
                  command=self.delete_selected_task).pack(side=tk.LEFT, padx=5)

        ttk.Button(task_buttons_frame, text="📊 导出任务",
                  command=self.export_current_tasks).pack(side=tk.LEFT, padx=5)

        ttk.Button(task_buttons_frame, text="🗑️ 清空已完成",
                  command=self.clear_completed_tasks).pack(side=tk.LEFT, padx=5)

    def load_profiles(self):
        """加载配置文件列表（异步优化版本）"""
        # 防重复加载检查
        if hasattr(self, '_loading_profiles') and self._loading_profiles:
            return

        def _load_in_background():
            try:
                self._loading_profiles = True

                # 重新加载配置文件（从磁盘重新读取）
                self.config_manager.reload_profiles()

                profiles = self.config_manager.get_all_profiles()
                all_profile_names = list(profiles.keys())

                # 同步配置到Excel跟踪器
                self.usage_tracker.sync_with_config_manager(self.config_manager)

                # 获取可用（未使用）的配置列表
                available_profiles = self.get_available_profiles()

                # 在主线程中更新UI
                def _update_ui():
                    try:
                        # 下拉框只显示可用的配置
                        self.profile_combo['values'] = available_profiles

                        # 自动选择下一个未使用的配置
                        if available_profiles:
                            next_profile = self.usage_tracker.get_next_unused_profile()
                            if next_profile:
                                self.profile_var.set(next_profile)
                                self.current_profile_name = next_profile

                            self.log(f"✅ 加载了 {len(all_profile_names)} 个配置文件，{len(available_profiles)} 个可用")
                        else:
                            self.log("⚠️ 没有可用的配置文件")
                            self.profile_var.set("")
                            self.current_profile_name = None

                        # 更新配置统计
                        self.update_profile_stats()

                    except Exception as e:
                        self.log(f"❌ 更新配置UI失败: {e}")
                    finally:
                        self._loading_profiles = False

                self.root.after(0, _update_ui)

            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ 加载配置文件失败: {e}"))
                self._loading_profiles = False

        # 在后台线程中执行加载操作
        import threading
        threading.Thread(target=_load_in_background, daemon=True).start()

    def get_available_profiles(self):
        """获取可用（未使用）的配置列表 - 优化版本，减少日志输出"""
        try:
            stats = self.usage_tracker.get_usage_stats()
            available_profiles = []

            for profile in stats['profiles']:
                # 只有状态为"未使用"的配置才算可用
                if profile['status'] == '未使用':
                    available_profiles.append(profile['name'])

            # 只在配置数量发生变化时才记录日志
            current_stats = f"总计{stats['total_count']}个配置，可用{len(available_profiles)}个"
            if not hasattr(self, '_last_profile_stats') or self._last_profile_stats != current_stats:
                self.log(f"📊 配置统计: {current_stats}")
                self._last_profile_stats = current_stats

            return available_profiles

        except Exception as e:
            self.log(f"❌ 获取可用配置失败: {e}")
            return []

    def update_profile_stats(self):
        """更新配置统计信息"""
        try:
            stats = self.usage_tracker.get_usage_stats()

            # 详细统计各种状态
            unused_count = 0
            used_count = 0
            moved_count = 0
            deleted_count = 0

            for profile in stats['profiles']:
                status = profile['status']
                if status == '未使用':
                    unused_count += 1
                elif status == '已使用':
                    used_count += 1
                elif status == '已移动':
                    moved_count += 1
                elif status == '已删除':
                    deleted_count += 1

            # 更新统计文本
            stats_text = f"剩余: {unused_count} | 已用: {used_count} | 总计: {stats['total_count']}"
            if moved_count > 0 or deleted_count > 0:
                stats_text += f" | 已移动: {moved_count} | 已删除: {deleted_count}"

            self.profile_stats_var.set(stats_text)

            # 只在统计发生变化时才记录日志
            if not hasattr(self, '_last_stats_text') or self._last_stats_text != stats_text:
                self.log(f"📊 配置统计更新: {stats_text}")
                self._last_stats_text = stats_text

        except Exception as e:
            error_msg = f"统计信息获取失败: {e}"
            self.profile_stats_var.set("统计信息获取失败")
            self.log(f"❌ {error_msg}")

    def start_api_server(self):
        """启动API服务器 - 优化Cloudflare Tunnel兼容性"""
        def run_server():
            try:
                self.log("🚀 启动API服务器")
                print("Flask app starting...")
                host = self.config['api']['host']
                port = self.config['api']['port']

                # 配置Flask应用以更好地支持Cloudflare Tunnel - 学习learn版本的简洁配置
                self.app.config.update({
                    'SEND_FILE_MAX_AGE_DEFAULT': 0,  # 禁用缓存
                    'PROPAGATE_EXCEPTIONS': True,    # 传播异常
                    'PREFERRED_URL_SCHEME': 'https', # 优先使用HTTPS
                    'SERVER_NAME': None,             # 允许任何域名
                })

                # 启动Flask服务器 - 学习learn版本的简洁配置
                self.app.run(
                    host=host,
                    port=port,
                    debug=False,
                    threaded=True,
                    use_reloader=False,
                    request_handler=None,  # 使用默认处理器
                    passthrough_errors=False  # 不传递错误到WSGI
                )
            except Exception as e:
                self.log(f"❌ API服务器启动失败: {str(e)}")
                print(f"Flask error: {e}")

        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()

        # 延迟更新状态，给服务器更多启动时间
        port = self.config['api']['port']
        self.root.after(3000, lambda: self.api_status_var.set(f"🟢 运行中 (端口:{port})"))
        self.api_server_running = True
        self.log("✅ API服务器启动线程已创建")





    def start_task_processor(self):
        """启动任务处理器"""
        processor_thread = threading.Thread(target=self.task_processing_loop, daemon=True)
        processor_thread.start()
        self.log("🔄 任务处理器已启动")

    def start_timeout_checker(self):
        """启动超时检查器"""
        timeout_thread = threading.Thread(target=self.timeout_check_loop, daemon=True)
        timeout_thread.start()
        self.log(f"⏰ 任务超时检查器已启动 (超时时间: {self.task_timeout_seconds}秒)")

    def timeout_check_loop(self):
        """超时检查循环 - 优化版本，减少不必要的检查"""
        while self.timeout_check_enabled:
            try:
                if self.is_processing and self.current_task and self.task_start_time:
                    current_time = time.time()
                    elapsed_time = current_time - self.task_start_time
                    remaining_time = self.task_timeout_seconds - elapsed_time

                    # 更新超时状态显示（降低频率）
                    if remaining_time > 0:
                        self.root.after(0, lambda: self.timeout_status_var.set(
                            f"⏰ 任务运行中 (剩余: {remaining_time:.0f}秒)"
                        ))

                    if elapsed_time > self.task_timeout_seconds:
                        self.log(f"⏰ 检测到任务超时: {self.current_task.get('email', 'Unknown')} (运行时间: {elapsed_time:.1f}秒)")
                        self.handle_task_timeout()

                    # 处理任务时，每15秒检查一次
                    sleep_time = 15
                else:
                    # 没有任务在处理时显示正常状态
                    self.root.after(0, lambda: self.timeout_status_var.set(
                        f"⏰ 超时检查已启用 ({self.task_timeout_seconds}秒)"
                    ))

                    # 空闲时，每60秒检查一次
                    sleep_time = 60

                time.sleep(sleep_time)

            except Exception as e:
                self.log(f"❌ 超时检查异常: {e}")
                time.sleep(60)  # 出错时等待1分钟再继续

    def handle_task_timeout(self):
        """处理任务超时"""
        try:
            if not self.current_task:
                return

            task_id = self.current_task.get('id')
            task_email = self.current_task.get('email', 'Unknown')
            elapsed_time = time.time() - self.task_start_time if self.task_start_time else 0

            self.log(f"🚨 任务超时处理开始: ID={task_id}, Email={task_email}, 运行时间={elapsed_time:.1f}秒")

            # 1. 强制停止当前任务处理
            self.is_processing = False

            # 2. 尝试关闭浏览器
            try:
                profile_name = self.profile_var.get()
                if profile_name:
                    self.log(f"🔒 强制关闭超时任务的浏览器: {profile_name}")
                    self.browser_manager.close_browser(profile_name)
                    time.sleep(3)  # 等待浏览器关闭
            except Exception as e:
                self.log(f"⚠️ 关闭超时任务浏览器失败: {e}")

            # 3. 标记任务为失败
            timeout_message = f"任务超时 (运行时间: {elapsed_time:.1f}秒，超时限制: {self.task_timeout_seconds}秒)"
            self.fail_task(task_id, timeout_message)

            # 4. 重置状态
            self.current_task = None
            self.task_start_time = None

            # 5. 更新界面
            self.root.after(0, self.refresh_task_display)

            self.log(f"✅ 超时任务处理完成: {task_email}")

        except Exception as e:
            self.log(f"❌ 处理任务超时异常: {e}")
            # 确保状态重置
            self.is_processing = False
            self.current_task = None
            self.task_start_time = None

    def start_status_update(self):
        """启动状态更新（事件驱动模式，最小化数据库连接）"""
        self.log("📋 状态更新已切换为事件驱动模式，按需更新")

        # 事件驱动的状态监控
        def event_driven_update_loop():
            self.status_update_enabled = True

            while self.status_update_enabled:
                try:
                    # 只在特定条件下更新状态
                    should_update = False

                    # 条件1：正在处理任务时，需要更新进度
                    if self.is_processing:
                        should_update = True
                        sleep_time = 120  # 处理任务时2分钟更新一次
                    else:
                        # 条件2：空闲时间过长，偶尔更新一次状态
                        if not hasattr(self, 'last_idle_update'):
                            self.last_idle_update = time.time()

                        if time.time() - self.last_idle_update > 600:  # 10分钟更新一次
                            should_update = True
                            self.last_idle_update = time.time()

                        sleep_time = 300  # 空闲时5分钟检查一次

                    # 按需更新状态（避免频繁数据库连接）
                    if should_update:
                        self.root.after(0, self._update_basic_status_only)

                    time.sleep(sleep_time)

                except Exception as e:
                    self.log(f"❌ 状态更新异常: {e}")
                    time.sleep(300)  # 异常时等待5分钟

        update_thread = threading.Thread(target=event_driven_update_loop, daemon=True)
        update_thread.start()

    def _update_basic_status_only(self):
        """按需更新基本状态，避免频繁数据库连接"""
        # 检查是否真的需要更新（避免无意义的数据库连接）
        if not hasattr(self, '_last_status_update'):
            self._last_status_update = 0

        current_time = time.time()
        if current_time - self._last_status_update < 60:  # 1分钟内不重复更新
            return

        try:
            # 快速连接，获取必要信息后立即断开
            with get_sqlite_connection('tasks.db', timeout=5) as conn:

                # 使用最简单的查询获取关键信息
                cursor = conn.execute('''
                    SELECT
                        (SELECT is_processing FROM system_status WHERE id = 1) as is_processing,
                        (SELECT current_email FROM system_status WHERE id = 1) as current_email
                ''')

                status_result = cursor.fetchone()

                # 安全地获取状态信息
                is_processing = self.safe_get_db_value(status_result, 'is_processing') or self.safe_get_db_value(status_result, 0)
                current_email = self.safe_get_db_value(status_result, 'current_email') or self.safe_get_db_value(status_result, 1)

                # 只在处理任务时才查询队列统计，空闲时跳过
                if is_processing:
                    cursor = conn.execute('''
                        SELECT
                            COUNT(CASE WHEN status = 'waiting' THEN 1 END) as waiting,
                            COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing
                        FROM task_queue
                        WHERE DATE(created_at) = DATE('now')
                    ''')
                    queue_result = cursor.fetchone()

                    waiting = self.safe_get_db_value(queue_result, 'waiting') or self.safe_get_db_value(queue_result, 0) or 0
                    processing = self.safe_get_db_value(queue_result, 'processing') or self.safe_get_db_value(queue_result, 1) or 0
                else:
                    waiting = 0
                    processing = 0

                # 更新UI显示
                if is_processing and current_email:
                    self.current_status_var.set(f"🔄 处理中: {current_email}")
                    self.queue_stats_var.set(f"等待: {waiting}, 处理中: {processing}")
                else:
                    self.current_status_var.set("🟢 空闲中")
                    self.queue_stats_var.set("队列空闲 (点击刷新查看详情)")

                self._last_status_update = current_time

        except sqlite3.OperationalError as e:
            if "database is locked" not in str(e):
                self.log(f"⚠️ 状态更新失败: {e}")
            # 数据库锁定时静默忽略
        except Exception as e:
            self.log(f"⚠️ 状态更新异常: {e}")

    def _toggle_auto_refresh(self):
        """切换自动刷新模式"""
        if self.auto_refresh_var.get():
            # 启用自动刷新
            interval = self.refresh_interval_var.get()
            self.log(f"🔄 已启用自动刷新模式 (间隔: {interval}秒)")
            self.refresh_hint_var.set(f"🔄 自动刷新已启用，每{interval}秒更新一次")
            self._start_auto_refresh()
        else:
            # 禁用自动刷新
            self.log("⏸️ 已禁用自动刷新模式，切换为手动刷新")
            self.refresh_hint_var.set("💡 手动刷新模式，按F5或点击刷新按钮更新数据")
            self._stop_auto_refresh()

    def _start_auto_refresh(self):
        """启动自动刷新"""
        if hasattr(self, '_auto_refresh_running') and self._auto_refresh_running:
            return

        self._auto_refresh_running = True

        def auto_refresh_loop():
            while self._auto_refresh_running:
                try:
                    if self.auto_refresh_var.get():  # 双重检查
                        interval = int(self.refresh_interval_var.get())
                        time.sleep(interval)

                        if self._auto_refresh_running:  # 再次检查
                            self.root.after(0, self.refresh_task_display)
                    else:
                        break
                except Exception as e:
                    self.log(f"⚠️ 自动刷新异常: {e}")
                    time.sleep(10)

        threading.Thread(target=auto_refresh_loop, daemon=True).start()

    def _stop_auto_refresh(self):
        """停止自动刷新"""
        self._auto_refresh_running = False

    def task_processing_loop(self):
        """任务处理循环 - 重构为事件驱动模式，按需连接数据库"""
        self.log("🔄 任务处理循环已启动 (事件驱动模式)")

        # 初始化任务处理状态
        self.task_check_enabled = True
        self.task_check_triggered = False
        last_status_check = 0

        while self.task_check_enabled:
            try:
                current_time = time.time()

                # 只在不处理任务时检查新任务
                if not self.is_processing:
                    # 检查是否有事件触发或定时检查
                    should_check = False

                    if self.task_check_triggered:
                        # 事件触发的检查
                        should_check = True
                        self.task_check_triggered = False
                        self.log("🔔 响应任务检查事件")
                    elif not hasattr(self, '_last_routine_check') or \
                         current_time - self._last_routine_check > 60:  # 1分钟定时检查
                        should_check = True
                        self._last_routine_check = current_time

                    if should_check:
                        # 检查是否有新任务（按需连接数据库）
                        task = self.get_next_task_if_available()
                        if task:
                            # 处理sqlite3.Row对象
                            if hasattr(task, 'keys'):
                                task_dict = dict(task)
                            else:
                                task_dict = task
                            self.log(f"📋 获取到任务: {task_dict['id']} - {task_dict['email']}")
                            self.process_task(task)
                        else:
                            # 没有任务时，休眠较长时间
                            time.sleep(30)
                    else:
                        # 没有触发事件时，短暂休眠后继续检查
                        time.sleep(5)
                else:
                    # 正在处理任务时，等待更长时间
                    time.sleep(15)

                # 定期检查系统状态一致性（降低频率）
                if current_time - last_status_check > 1800:  # 30分钟检查一次
                    self.check_system_status_consistency()
                    last_status_check = current_time

            except Exception as e:
                self.log(f"❌ 任务处理循环异常: {e}")
                import traceback
                self.log(f"详细错误: {traceback.format_exc()}")
                time.sleep(60)  # 异常时等待1分钟

    def get_next_task(self):
        """获取下一个待处理任务（优化版本，减少数据库锁定）"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with get_sqlite_connection('tasks.db', timeout=20) as conn:

                    # 分步操作：先获取任务，再更新状态
                    beijing_time = self.get_beijing_time()

                    # 1. 快速检查是否有等待任务（避免不必要的复杂查询）
                    cursor = conn.execute('SELECT COUNT(*) as count FROM task_queue WHERE status = "waiting"')
                    waiting_count = cursor.fetchone()['count']

                    if waiting_count == 0:
                        return None  # 没有等待任务，直接返回

                    # 2. 获取下一个等待中的任务
                    cursor = conn.execute('''
                        SELECT * FROM task_queue
                        WHERE status = 'waiting'
                        ORDER BY queue_position ASC
                        LIMIT 1
                    ''')
                    task = cursor.fetchone()

                    if task:
                        # 3. 更新任务状态为处理中
                        conn.execute('''
                            UPDATE task_queue
                            SET status = 'processing',
                                started_at = ?
                            WHERE id = ?
                        ''', (beijing_time, task['id']))

                    if task:
                        # 4. 更新系统状态
                        conn.execute('''
                            UPDATE system_status
                            SET current_processing_task_id = ?,
                                current_email = ?,
                                is_processing = TRUE,
                                started_at = ?
                            WHERE id = 1
                        ''', (task['id'], task['email'], beijing_time))

                        self.log(f"📋 开始处理任务: {task['email']}")

                        # 异步更新用户提交历史状态
                        threading.Thread(
                            target=self.update_user_submission_status,
                            args=(task['id'], 'processing'),
                            daemon=True
                        ).start()

                    return task

            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2  # 指数退避：2s, 4s, 6s
                    self.log(f"⚠️ 数据库锁定，{wait_time}秒后重试 (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(wait_time)
                    continue
                else:
                    # 过滤掉无意义的异常值（如数字0）
                    if str(e).strip() and str(e) != '0':
                        self.log(f"❌ 数据库操作失败: {e}")
                    return None
            except Exception as e:
                if attempt < max_retries - 1:
                    # 过滤掉无意义的异常值
                    if str(e).strip() and str(e) != '0':
                        self.log(f"⚠️ 获取任务失败，重试中: {e}")
                    time.sleep(2)
                    continue
                else:
                    # 过滤掉无意义的异常值
                    if str(e).strip() and str(e) != '0':
                        self.log(f"❌ 获取任务最终失败: {e}")
                    return None

        return None  # 所有重试都失败

    def get_next_task_if_available(self):
        """按需检查是否有可用任务，避免频繁数据库连接"""
        try:
            # 快速检查：先看看是否有等待任务，避免复杂查询
            with get_sqlite_connection('tasks.db', timeout=5) as conn:

                # 只做简单的计数查询
                cursor = conn.execute('SELECT COUNT(*) as count FROM task_queue WHERE status = "waiting"')
                result = cursor.fetchone()

                # 安全地获取计数值
                waiting_count = self.safe_get_db_value(result, 'count') or self.safe_get_db_value(result, 0) or 0

                if waiting_count == 0:
                    return None  # 没有等待任务，直接返回，避免复杂查询

                # 有等待任务时，才执行完整的获取逻辑
                return self.get_next_task()

        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                # 数据库锁定时返回None，避免阻塞
                return None
            else:
                self.log(f"⚠️ 检查任务可用性失败: {e}")
                return None
        except Exception as e:
            self.log(f"⚠️ 任务可用性检查异常: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")
            return None

    def trigger_task_check(self):
        """触发任务检查事件，通知任务处理循环立即检查新任务"""
        # 设置事件标志，让任务处理循环立即检查
        self.task_check_triggered = True
        self.log("🔔 已触发任务检查事件")

    def stop_task_processing(self):
        """停止任务处理循环"""
        self.task_check_enabled = False
        self.log("⏹️ 任务处理循环已停止")

    def safe_get_db_value(self, result, key_or_index):
        """安全地从数据库结果中获取值，兼容Row对象和元组"""
        try:
            if result is None:
                return None

            if hasattr(result, 'keys'):  # Row对象
                return result[key_or_index]
            else:  # 元组
                if isinstance(key_or_index, str):
                    # 如果传入的是字符串键但结果是元组，返回None
                    return None
                return result[key_or_index]
        except (KeyError, IndexError, TypeError):
            return None

    def process_task(self, task):
        """处理单个任务"""
        self.is_processing = True
        self.current_task = task
        self.task_start_time = time.time()  # 记录任务开始时间

        # 将sqlite3.Row对象转换为字典以便统一访问
        if hasattr(task, 'keys'):  # 检查是否是Row对象
            task_dict = dict(task)
        else:
            task_dict = task

        self.current_task = task_dict  # 更新为字典格式

        self.log(f"⏰ 任务开始处理: {task_dict.get('email', 'Unknown')} (超时限制: {self.task_timeout_seconds}秒)")

        try:
            # 确保浏览器运行 - 自动选择可用配置
            profile_name = self.profile_var.get()
            if not profile_name:
                # 自动选择第一个可用的配置
                available_profiles = self.get_available_profiles()
                if available_profiles:
                    profile_name = available_profiles[0]
                    self.profile_var.set(profile_name)
                    self.log(f"🔄 自动选择配置: {profile_name}")
                else:
                    raise Exception("没有可用的配置文件，请先创建配置")

            browser_started = self.ensure_browser_running(profile_name)
            if not browser_started:
                raise Exception("浏览器启动失败")

            # 执行AddmemClick
            result = self.execute_addmem_click(profile_name, task_dict['email'])

            # 更新任务状态
            if result['success']:
                self.complete_task(task_dict['id'], result['message'])
                self.log(f"✅ 任务完成: {task_dict['email']}")

                # 任务完成后，优雅关闭浏览器但保留配置文件
                self.graceful_close_browser_after_completion(profile_name)

            else:
                # 任务失败，执行完整的失败处理流程
                self.fail_task(task_dict['id'], result['error'])
                self.log(f"❌ 任务失败: {task_dict['email']} - {result['error']}")
                # fail_task方法会处理浏览器关闭和切换配置

        except Exception as e:
            error_msg = f"处理异常: {str(e)}"
            self.log(f"❌ 任务处理异常: {task_dict['email']} - {error_msg}")
            # 执行完整的失败处理流程
            self.fail_task(task_dict['id'], error_msg)

        finally:
            # 确保系统状态正确重置（无论成功还是失败）
            self.is_processing = False
            self.current_task = None
            self.task_start_time = None  # 重置任务开始时间

            # 触发事件：任务处理完成，通知系统检查新任务
            self.trigger_task_check()

            # 任务处理完成后自动刷新任务和配置（延迟更长时间，减少频率）
            self.root.after(1000, self._update_basic_status_only)
            self.root.after(2000, self.refresh_task_display)  # 自动刷新任务列表
            self.root.after(5000, self.load_profiles)         # 自动刷新配置列表
            self.log("✅ 任务处理完成，已触发新任务检查")

    def graceful_close_browser_after_completion(self, profile_name):
        """任务完成后优雅关闭浏览器，但保留配置文件"""
        try:
            self.log(f"🔄 开始优雅关闭浏览器: {profile_name}")

            # 1. 先尝试通过自动化引擎优雅关闭
            try:
                self.log("🔒 尝试优雅关闭浏览器窗口...")

                close_result = self.graceful_close_browser_window(profile_name)

                if close_result:
                    self.log("✅ 浏览器窗口已优雅关闭")
                    # 增加等待时间，确保所有数据完全保存
                    time.sleep(10)  # 从3秒增加到10秒
                else:
                    self.log("⚠️ 优雅关闭失败，使用标准关闭方式")
                    if self.browser_manager.close_browser(profile_name):
                        self.log(f"🔒 浏览器关闭成功")
                    else:
                        self.log(f"⚠️ 浏览器关闭失败或未运行")

                    # 增加等待时间，确保浏览器完全关闭并保存数据
                    time.sleep(8)  # 从2秒增加到8秒

            except Exception as e:
                self.log(f"❌ 关闭浏览器异常: {e}")

            # 2. 标记当前配置为已使用（仅在Excel中记录，不移动文件）
            try:
                current_task_email = getattr(self.current_task, 'get', lambda x: None)('email') if self.current_task else None
                if self.usage_tracker.mark_profile_used(profile_name, current_task_email):
                    self.log(f"✅ 配置使用状态已记录到Excel: {profile_name}")
                else:
                    self.log(f"⚠️ 记录配置使用状态失败: {profile_name}")

            except Exception as e:
                self.log(f"❌ 记录配置使用状态异常: {e}")

            # 3. 获取下一个未使用的配置（如果有的话）
            try:
                next_profile = self.usage_tracker.get_next_unused_profile()

                if next_profile:
                    self.profile_var.set(next_profile)
                    self.current_profile_name = next_profile
                    self.log(f"🔄 切换到下一个可用配置: {next_profile}")

                    # 更新界面 - 延迟重新加载可用配置列表，避免频繁调用
                    self.root.after(2000, self.load_profiles)  # 延迟2秒

                else:
                    self.log("⚠️ 没有更多未使用的配置，请手动管理配置文件")
                    # 保持当前配置选择，不清空
                    # self.profile_var.set("")
                    # self.current_profile_name = None

            except Exception as e:
                self.log(f"❌ 获取下一个配置失败: {e}")

            self.log(f"✅ 浏览器优雅关闭完成，配置文件已保留")

        except Exception as e:
            self.log(f"❌ 优雅关闭浏览器过程异常: {e}")

    def force_close_browser_processes(self, profile_name):
        """已禁用：不再强制关闭浏览器进程以保护数据"""
        self.log(f"🛡️ 跳过强制关闭浏览器进程 {profile_name}，保护浏览器数据")
        # 此方法已被禁用以保护浏览器数据
        # 不再进行任何强制关闭操作
        return

    def graceful_close_browser_window(self, profile_name):
        """优雅地关闭浏览器窗口，模拟手动关闭"""
        try:
            self.log(f"🪟 尝试优雅关闭浏览器窗口: {profile_name}")

            # 方法1: 通过Selenium的quit方法（最可靠）
            try:
                self.log("🔧 尝试Selenium优雅关闭...")

                # 获取WebDriver实例
                driver = self.browser_manager.get_automation_driver(profile_name)
                if driver:
                    # 先保存当前页面状态
                    try:
                        # 执行保存操作
                        driver.execute_script("window.onbeforeunload = null;")  # 移除beforeunload事件
                        time.sleep(2)  # 等待保存
                    except:
                        pass

                    # 逐个关闭窗口
                    window_handles = driver.window_handles.copy()
                    for handle in window_handles:
                        try:
                            driver.switch_to.window(handle)
                            driver.close()
                            time.sleep(1)  # 每关闭一个窗口等待1秒
                        except:
                            continue

                    # 最后quit driver
                    try:
                        driver.quit()
                        self.log("✅ Selenium优雅关闭成功")
                        time.sleep(8)  # 增加等待时间，确保数据完全保存
                        return True
                    except:
                        pass

            except Exception as e:
                self.log(f"⚠️ Selenium关闭失败: {e}")

            # 方法2: 通过系统API发送优雅关闭消息 (Windows)
            try:
                import platform
                if platform.system() == "Windows":
                    self.log("🪟 尝试Windows API优雅关闭...")

                    # 获取浏览器进程信息
                    if profile_name in self.browser_manager.running_browsers:
                        browser_info = self.browser_manager.running_browsers[profile_name]
                        pid = browser_info.get('pid')

                        if pid:
                            # 使用taskkill发送优雅关闭信号（不强制）
                            import subprocess
                            result = subprocess.run(
                                ['taskkill', '/PID', str(pid)],
                                capture_output=True,
                                text=True,
                                timeout=30
                            )

                            if result.returncode == 0:
                                self.log("✅ Windows API优雅关闭成功")
                                time.sleep(10)  # 增加等待时间
                                return True
                            else:
                                self.log(f"⚠️ Windows API关闭失败: {result.stderr}")

            except Exception as e:
                self.log(f"⚠️ Windows API关闭失败: {e}")

            # 方法3: 通过JavaScript关闭窗口
            try:
                close_script = """
                    // 移除beforeunload事件防止弹窗
                    window.onbeforeunload = null;

                    // 尝试标准关闭
                    if (window.close) {
                        window.close();
                        return {method: 'window.close', success: true};
                    }

                    return {method: 'none', success: false};
                """

                result = self.automation_engine.execute_script(profile_name, close_script)
                if result and result.get('success'):
                    self.log(f"✅ JavaScript关闭成功: {result.get('method', 'unknown')}")
                    time.sleep(5)  # 增加等待时间，确保数据保存
                    return True

            except Exception as e:
                self.log(f"⚠️ JavaScript关闭失败: {e}")

            # 方法4: 使用pyautogui模拟Alt+F4关闭
            try:
                try:
                    import pyautogui
                    import win32gui
                    import win32con
                except ImportError as e:
                    self.log(f"⚠️ 缺少依赖库: {e}，跳过Alt+F4方法")
                    raise Exception("缺少依赖库")

                self.log("⌨️ 尝试模拟Alt+F4关闭...")

                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)
                        if 'chrome' in window_title.lower() or 'augment' in window_title.lower():
                            windows.append((hwnd, window_title))
                    return True

                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)

                if windows:
                    # 激活第一个Chrome窗口
                    hwnd = windows[0][0]
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(1)

                    # 发送Alt+F4
                    pyautogui.hotkey('alt', 'f4')
                    self.log("✅ Alt+F4快捷键发送成功")
                    time.sleep(5)
                    return True

            except Exception as e:
                self.log(f"⚠️ Alt+F4快捷键失败: {e}")

            self.log("❌ 所有优雅关闭方法都失败了")
            return False

        except Exception as e:
            self.log(f"❌ 优雅关闭浏览器窗口异常: {e}")
            return False

    def remove_lock_files(self, profile_path):
        """删除配置文件夹中的LOCK文件"""
        try:
            import glob

            # 查找所有LOCK文件
            lock_patterns = [
                os.path.join(profile_path, "**", "LOCK"),
                os.path.join(profile_path, "**", "*.lock"),
                os.path.join(profile_path, "**", "lockfile")
            ]

            removed_count = 0
            for pattern in lock_patterns:
                lock_files = glob.glob(pattern, recursive=True)
                for lock_file in lock_files:
                    try:
                        os.remove(lock_file)
                        removed_count += 1
                        self.log(f"🗑️ 删除锁文件: {os.path.basename(lock_file)}")
                    except Exception as e:
                        self.log(f"⚠️ 无法删除锁文件: {os.path.basename(lock_file)}")

            if removed_count > 0:
                self.log(f"✅ 删除了 {removed_count} 个锁文件")

        except Exception as e:
            self.log(f"❌ 删除锁文件失败")

    def ensure_browser_running(self, profile_name):
        """确保浏览器运行并检查登录状态（与main.py保持一致的启动方式）"""
        try:
            # 检查配置是否存在
            profile = self.config_manager.get_profile(profile_name)
            if not profile:
                self.log(f"❌ 配置 {profile_name} 不存在")
                return False

            self.log(f"🚀 确保浏览器运行: {profile_name}")
            self.log(f"📁 配置路径: {profile.get('path', 'N/A')}")
            self.log(f"🎭 Stealth模式: {profile.get('stealth_enabled', False)}")

            # 检查是否已经在运行
            if profile_name in self.browser_manager.running_browsers:
                # 验证是否真的在运行
                is_running = self.browser_manager._comprehensive_browser_check(
                    self.browser_manager.running_browsers[profile_name]
                )
                if is_running:
                    self.log(f"✅ 浏览器已在运行，检查登录状态...")
                    # 检查登录状态
                    login_status = self.check_login_status(profile_name)
                    if login_status:
                        self.log(f"✅ 登录状态有效")
                    else:
                        self.log(f"⚠️ 登录状态可能已失效，但继续使用当前会话")
                    return True
                else:
                    self.log(f"🧹 清理无效的浏览器记录: {profile_name}")
                    del self.browser_manager.running_browsers[profile_name]

            # 启动浏览器（会自动恢复上次会话）
            self.log(f"🚀 启动浏览器")
            browser_info = self.browser_manager.launch_browser(profile_name)

            if browser_info:
                stealth_mode = browser_info.get('stealth_mode', False)
                pid = browser_info.get('pid', 'N/A')

                self.log(f"✅ 浏览器启动成功: {profile_name}")
                self.log(f"🔧 模式: {'Stealth' if stealth_mode else '标准'}")
                self.log(f"🆔 PID: {pid}")

                # 对于新启动的浏览器，等待一段时间让会话恢复完成
                if not browser_info.get('existing_instance', False):
                    self.log(f"⏳ 等待会话恢复完成...")
                    time.sleep(5)  # 给Chrome更多时间来恢复会话
                else:
                    self.log(f"🔄 复用现有实例，会话应该已经存在")
                    time.sleep(2)  # 稍微等待一下

                # 检查登录状态
                login_status = self.check_login_status(profile_name)
                if login_status:
                    self.log(f"✅ 检测到有效登录状态")
                else:
                    self.log(f"⚠️ 未检测到登录状态，可能需要重新登录")

                return True
            else:
                self.log(f"❌ 浏览器启动失败")
                return False

        except Exception as e:
            self.log(f"❌ 确保浏览器运行失败: {e}")
            return False

    def check_login_status(self, profile_name):
        """检查浏览器登录状态"""
        try:
            self.log("🔍 检查登录状态...")

            # 访问团队页面检查登录状态
            team_url = "https://app.augmentcode.com/account/team"
            self.automation_engine.navigate_to_url(profile_name, team_url)
            time.sleep(3)

            # 检查页面内容来判断登录状态
            login_check_result = self.automation_engine.execute_script(profile_name, """
                // 检查是否在登录页面或已登录页面
                const currentUrl = window.location.href;
                const pageTitle = document.title;
                const bodyText = document.body ? document.body.textContent : '';

                // 检查是否包含登录相关元素
                const hasLoginForm = document.querySelector('form[action*="login"]') ||
                                   document.querySelector('input[type="email"]') ||
                                   document.querySelector('input[type="password"]') ||
                                   document.querySelector('button[type="submit"]');

                // 检查是否包含团队页面相关元素
                const hasTeamElements = document.querySelector('[data-testid*="team"]') ||
                                      document.querySelector('button[aria-label*="Add"]') ||
                                      bodyText.includes('Add members') ||
                                      bodyText.includes('Team') ||
                                      bodyText.includes('Members');

                // 检查是否有用户信息
                const hasUserInfo = document.querySelector('[data-testid*="user"]') ||
                                  document.querySelector('.user-menu') ||
                                  document.querySelector('[aria-label*="user"]');

                return {
                    currentUrl: currentUrl,
                    pageTitle: pageTitle,
                    hasLoginForm: !!hasLoginForm,
                    hasTeamElements: !!hasTeamElements,
                    hasUserInfo: !!hasUserInfo,
                    isLoggedIn: !hasLoginForm && (hasTeamElements || hasUserInfo),
                    bodyTextLength: bodyText.length
                };
            """)

            if login_check_result:
                is_logged_in = login_check_result.get('isLoggedIn', False)
                current_url = login_check_result.get('currentUrl', '')

                if is_logged_in:
                    self.log("✅ 检测到已登录状态")
                    return True
                elif 'login' in current_url.lower():
                    self.log("⚠️ 当前在登录页面，需要重新登录")
                    return False
                else:
                    self.log("⚠️ 登录状态不明确，但继续执行")
                    return True
            else:
                self.log("⚠️ 无法检测登录状态")
                return True  # 默认继续执行

        except Exception as e:
            self.log(f"⚠️ 检查登录状态异常: {e}")
            return True  # 出错时默认继续执行

    def execute_addmem_click(self, profile_name, email):
        """执行AddmemClick操作"""
        try:
            self.log(f"🎯 开始添加成员: {email}")

            # 1. 访问Augment团队页面
            self.log("📄 访问团队页面")
            team_url = "https://app.augmentcode.com/account/team"
            self.automation_engine.navigate_to_url(profile_name, team_url)
            time.sleep(5)

            # 2. 检查页面加载状态
            self.log("🔍 检查页面加载状态")
            page_status = self.automation_engine.execute_script(profile_name, """
                return {
                    readyState: document.readyState,
                    url: window.location.href,
                    hasContent: document.body && document.body.children.length > 0
                };
            """)

            if not page_status or page_status.get('readyState') != 'complete':
                return {'success': False, 'error': '页面未完全加载'}

            self.log("✅ 页面加载完成")

            # 3. 查找并点击Add members按钮
            self.log("🔍 查找添加成员按钮")
            click_result = self.automation_engine.execute_script(profile_name, """
                const buttons = Array.from(document.querySelectorAll('button, a'));
                for (const button of buttons) {
                    const text = button.textContent.trim().toLowerCase();
                    if (text.includes('add') && text.includes('member')) {
                        button.click();
                        return {success: true, text: button.textContent.trim()};
                    }
                }
                return {success: false, message: '未找到Add members按钮'};
            """)

            if not click_result or not click_result.get('success'):
                return {'success': False, 'error': '未找到添加成员按钮'}

            self.log(f"✅ 点击添加成员按钮")
            time.sleep(3)

            # 4. 输入邮箱地址
            self.log(f"📧 输入邮箱地址")
            email_result = self.automation_engine.execute_script(profile_name, f"""
                const inputs = document.querySelectorAll('input, div[contenteditable="true"]');
                for (const input of inputs) {{
                    if (input.offsetParent !== null &&
                        (input.type === 'email' ||
                         input.placeholder?.includes('@') ||
                         input.getAttribute('data-testid') === 'token-input')) {{

                        const email = '{email}';

                        if (input.tagName.toLowerCase() === 'input') {{
                            input.value = email;
                            input.dispatchEvent(new Event('input', {{bubbles: true}}));
                        }} else {{
                            input.textContent = email;
                            input.dispatchEvent(new Event('input', {{bubbles: true}}));
                        }}

                        return {{success: true, email: email}};
                    }}
                }}
                return {{success: false, message: '未找到邮箱输入框'}};
            """)

            if not email_result or not email_result.get('success'):
                return {'success': False, 'error': '未找到邮箱输入框'}

            self.log("✅ 邮箱输入成功")
            time.sleep(2)

            # 5. 点击提交按钮
            self.log("🎯 查找提交按钮")
            submit_result = self.automation_engine.execute_script(profile_name, """
                let targetButton = null;
                let buttonText = '';
                let buttonClasses = '';

                // 获取所有按钮
                const allButtons = Array.from(document.querySelectorAll('button'));

                // 调试信息：列出所有按钮
                const buttonInfo = [];
                allButtons.forEach((button, index) => {
                    if (button.offsetParent !== null) {
                        const text = button.textContent.trim();
                        const classes = button.className;
                        const isDisabled = button.disabled ||
                                         button.hasAttribute('disabled') ||
                                         button.getAttribute('aria-disabled') === 'true' ||
                                         button.classList.contains('disabled');

                        buttonInfo.push({
                            index: index,
                            text: text,
                            classes: classes,
                            disabled: isDisabled
                        });
                    }
                });

                // 查找策略1: 精确匹配"Add 1 Member"
                for (const button of allButtons) {
                    if (button.offsetParent === null) continue;

                    const text = button.textContent.trim();
                    if (text === 'Add 1 Member') {
                        targetButton = button;
                        buttonText = text;
                        buttonClasses = button.className;
                        break;
                    }
                }

                // 查找策略2: 匹配包含"Add"和数字的按钮
                if (!targetButton) {
                    for (const button of allButtons) {
                        if (button.offsetParent === null) continue;

                        const text = button.textContent.trim();
                        if (text.includes('Add') && /\\d/.test(text) && text.includes('Member')) {
                            targetButton = button;
                            buttonText = text;
                            buttonClasses = button.className;
                            break;
                        }
                    }
                }

                // 查找策略3: 匹配rt-Button类且包含"Add"的按钮
                if (!targetButton) {
                    for (const button of allButtons) {
                        if (button.offsetParent === null) continue;

                        const text = button.textContent.trim();
                        const classes = button.className;
                        if (classes.includes('rt-Button') &&
                            classes.includes('rt-variant-solid') &&
                            text.includes('Add')) {
                            targetButton = button;
                            buttonText = text;
                            buttonClasses = classes;
                            break;
                        }
                    }
                }

                // 查找策略4: 匹配有data-accent-color属性且包含"Add"的按钮
                if (!targetButton) {
                    for (const button of allButtons) {
                        if (button.offsetParent === null) continue;

                        const text = button.textContent.trim();
                        if (button.hasAttribute('data-accent-color') && text.includes('Add')) {
                            targetButton = button;
                            buttonText = text;
                            buttonClasses = button.className;
                            break;
                        }
                    }
                }

                if (targetButton) {
                    // 检查按钮是否可点击
                    const isDisabled = targetButton.disabled ||
                                     targetButton.hasAttribute('disabled') ||
                                     targetButton.getAttribute('aria-disabled') === 'true' ||
                                     targetButton.classList.contains('disabled');

                    if (!isDisabled) {
                        // 先聚焦按钮，然后点击
                        targetButton.focus();

                        // 只使用标准点击方式，避免重复点击
                        targetButton.click();

                        return {
                            success: true,
                            clicked: true,
                            buttonText: buttonText,
                            buttonClasses: buttonClasses,
                            isDisabled: false
                        };
                    } else {
                        return {
                            success: true,
                            clicked: false,
                            buttonText: buttonText,
                            buttonClasses: buttonClasses,
                            isDisabled: true,
                            message: '按钮处于禁用状态'
                        };
                    }
                } else {
                    return {
                        success: true,
                        clicked: false,
                        found: false,
                        message: '未找到Add Member按钮',
                        availableButtons: buttonInfo
                    };
                }
            """)

            # 处理按钮点击结果
            if submit_result and submit_result.get('success'):
                if submit_result.get('clicked'):
                    # 成功点击了按钮
                    self.log(f"✅ 点击提交按钮成功")
                    time.sleep(3)

                elif submit_result.get('isDisabled'):
                    # 找到按钮但是禁用状态
                    self.log(f"⚠️ 按钮处于禁用状态")
                    return {'success': False, 'error': '按钮处于禁用状态'}

                else:
                    # 未找到按钮
                    self.log("❌ 未找到提交按钮")
                    return {'success': False, 'error': '未找到提交按钮'}
            else:
                return {'success': False, 'error': '按钮查找失败'}

            # 6. 等待操作完成并验证结果
            self.log("🔍 等待成员添加完成...")

            # 增加等待时间，确保添加操作完全完成
            max_wait_time = 15  # 最大等待15秒
            wait_interval = 1   # 每秒检查一次

            for attempt in range(max_wait_time):
                time.sleep(wait_interval)

                # 检查添加结果
                result_check = self.automation_engine.execute_script(profile_name, """
                    // 智能检查成功或错误提示，避免选择CSS/JS代码
                    function findValidMessages() {
                        // 1. 优化选择器，排除样式和脚本标签
                        const messageSelectors = [
                            '[role="alert"]',
                            '[role="status"]',
                            '.notification',
                            '.toast',
                            '.message',
                            '.alert',
                            '.success',
                            '.error',
                            '.warning',
                            '.info',
                            '[data-testid*="notification"]',
                            '[data-testid*="message"]',
                            '[data-testid*="alert"]',
                            '[class*="notification"]',
                            '[class*="toast"]',
                            '[class*="alert"]'
                        ];

                        let validMessages = [];

                        // 2. 使用专门的消息选择器
                        for (const selector of messageSelectors) {
                            try {
                                const elements = document.querySelectorAll(selector);
                                validMessages.push(...Array.from(elements));
                            } catch (e) {
                                // 忽略无效选择器
                            }
                        }

                        // 3. 如果没找到专门的消息元素，使用更严格的通用选择器
                        if (validMessages.length === 0) {
                            const generalElements = document.querySelectorAll('div, span, p');
                            for (const el of generalElements) {
                                // 排除不相关的元素
                                if (el.tagName === 'STYLE' ||
                                    el.tagName === 'SCRIPT' ||
                                    el.tagName === 'LINK' ||
                                    el.tagName === 'META' ||
                                    el.closest('style') ||
                                    el.closest('script') ||
                                    el.closest('head') ||
                                    el.closest('noscript')) {
                                    continue;
                                }

                                // 检查元素是否可见
                                if (el.offsetParent === null &&
                                    getComputedStyle(el).display === 'none') {
                                    continue;
                                }

                                // 检查文本长度，避免选择大段代码
                                const text = el.textContent.trim();
                                if (text.length > 500) {
                                    continue;
                                }

                                // 检查是否包含代码特征
                                if (text.includes('{') && text.includes('}') ||
                                    text.includes('function') ||
                                    text.includes('const ') ||
                                    text.includes('var ') ||
                                    text.includes('let ') ||
                                    text.includes('@scope') ||
                                    text.includes('!important') ||
                                    text.includes('font-weight') ||
                                    text.includes('font-size')) {
                                    continue;
                                }

                                validMessages.push(el);
                            }
                        }

                        return validMessages;
                    }

                    const messages = findValidMessages();

                    for (const msg of messages) {
                        const text = msg.textContent.trim().toLowerCase();
                        const originalText = msg.textContent.trim();

                        // 再次验证不是代码
                        if (originalText.length > 200 ||
                            originalText.includes('{') ||
                            originalText.includes('/*') ||
                            originalText.includes('*/') ||
                            originalText.includes('@scope') ||
                            originalText.includes('!important')) {
                            continue;
                        }

                        // 成功指示器
                        if (text.includes('success') ||
                            text.includes('added') ||
                            text.includes('invited') ||
                            text.includes('sent') ||
                            text.includes('member has been') ||
                            text.includes('invitation sent')) {
                            return {
                                success: true,
                                message: originalText,
                                completed: true
                            };
                        }

                        // 错误指示器
                        if (text.includes('error') ||
                            text.includes('failed') ||
                            text.includes('invalid') ||
                            text.includes('already exists') ||
                            text.includes('duplicate')) {
                            return {
                                success: false,
                                message: originalText,
                                completed: true
                            };
                        }
                    }

                    // 检查是否还在加载中
                    const loadingElements = document.querySelectorAll('[aria-busy="true"], .loading, .spinner, [data-loading="true"]');
                    const isLoading = loadingElements.length > 0;

                    return {
                        success: null,
                        message: '操作进行中...',
                        completed: !isLoading,
                        loading: isLoading
                    };
                """)

                if result_check:
                    if result_check.get('completed'):
                        # 操作已完成
                        if result_check.get('success') is True:
                            self.log(f"✅ 成员添加成功: {result_check.get('message', '成功')}")
                            return {
                                'success': True,
                                'message': f'成功添加成员: {email} - {result_check.get("message", "")}'
                            }
                        elif result_check.get('success') is False:
                            self.log(f"❌ 成员添加失败: {result_check.get('message', '失败')}")
                            return {
                                'success': False,
                                'error': f'添加失败: {result_check.get("message", "未知错误")}'
                            }
                    else:
                        # 仍在处理中
                        self.log(f"⏳ 等待添加完成... ({attempt + 1}/{max_wait_time}秒)")
                        continue

                # 如果没有明确的结果，继续等待
                self.log(f"⏳ 等待操作完成... ({attempt + 1}/{max_wait_time}秒)")

            # 超时后的最终检查
            self.log("⏰ 等待超时，进行最终检查...")
            final_check = self.automation_engine.execute_script(profile_name, """
                // 最终检查页面状态，避免包含CSS代码
                function getFinalStatus() {
                    // 检查页面中的文本内容，但排除样式和脚本
                    const bodyClone = document.body.cloneNode(true);

                    // 移除样式和脚本标签
                    const stylesToRemove = bodyClone.querySelectorAll('style, script, noscript, link[rel="stylesheet"]');
                    stylesToRemove.forEach(el => el.remove());

                    const cleanText = bodyClone.textContent.toLowerCase();

                    // 检查成功指示
                    if (cleanText.includes('member') &&
                        (cleanText.includes('added') ||
                         cleanText.includes('invited') ||
                         cleanText.includes('success'))) {
                        return {success: true, message: '成员添加操作可能已完成'};
                    }

                    // 检查错误指示
                    if (cleanText.includes('error') ||
                        cleanText.includes('failed') ||
                        cleanText.includes('invalid')) {
                        return {success: false, message: '添加操作可能失败'};
                    }

                    return {success: true, message: '添加操作已执行，请手动验证结果'};
                }

                return getFinalStatus();
            """)

            return {
                'success': True,
                'message': f'成员添加操作已完成: {email} - {final_check.get("message", "请手动验证")}'
            }

        except Exception as e:
            self.log(f"❌ 添加成员操作异常")
            return {
                'success': False,
                'error': f'添加成员操作异常'
            }

    def complete_task(self, task_id, message):
        """标记任务完成"""
        try:
            beijing_time = self.get_beijing_time()
            with get_sqlite_connection('tasks.db') as conn:
                # 获取任务信息，提取用户ID
                cursor = conn.execute('SELECT user_token FROM task_queue WHERE id = ?', (task_id,))
                task = cursor.fetchone()

                # 更新任务状态
                conn.execute('''
                    UPDATE task_queue
                    SET status = 'completed',
                        completed_at = ?,
                        result_message = ?
                    WHERE id = ?
                ''', (beijing_time, message, task_id))

                # 确保系统状态与任务状态一致 - 检查当前处理的任务ID
                cursor = conn.execute('''
                    SELECT current_processing_task_id FROM system_status WHERE id = 1
                ''')
                system_status = cursor.fetchone()

                # 如果系统状态显示正在处理当前任务，则重置系统状态
                if system_status and system_status[0] == task_id:
                    self.log(f"🔄 重置系统状态（任务完成）")
                    conn.execute('''
                        UPDATE system_status
                        SET current_processing_task_id = NULL,
                            current_email = NULL,
                            is_processing = FALSE,
                            last_activity = ?
                        WHERE id = 1
                    ''', (beijing_time,))

                conn.commit()  # 确保立即提交

                # 如果是用户任务，扣减用户次数
                if task and task[0].startswith('user_'):
                    try:
                        user_id = int(task[0].split('_')[1])
                        success = self.auth_manager.consume_user_quota(user_id)
                        if success:
                            self.log(f"📝 任务 {task_id} 完成，用户 {user_id} 次数已扣减")
                        else:
                            self.log(f"⚠️ 任务 {task_id} 完成，但用户 {user_id} 次数扣减失败")
                    except (ValueError, IndexError):
                        self.log(f"⚠️ 任务 {task_id} 完成，但无法解析用户ID")
                else:
                    self.log(f"📝 任务 {task_id} 状态已更新为完成")

                # 更新用户提交历史状态
                self.update_user_submission_status(task_id, 'completed', message)

                # 任务完成时自动刷新任务和配置
                self.root.after(1000, self.refresh_task_display)  # 自动刷新任务列表
                self.root.after(1500, self.load_profiles)         # 自动刷新配置列表
                self.log("✅ 任务已完成，已自动刷新任务和配置状态")

        except Exception as e:
            self.log(f"❌ 更新任务完成状态失败: {e}")

    def fail_task(self, task_id, error):
        """标记任务失败并执行失败处理流程"""
        try:
            beijing_time = self.get_beijing_time()

            # 获取任务信息
            task_info = None
            with get_sqlite_connection('tasks.db') as conn:
                cursor = conn.execute('''
                    SELECT email, profile_name FROM task_queue WHERE id = ?
                ''', (task_id,))
                task_info = cursor.fetchone()

                # 更新任务状态为失败
                conn.execute('''
                    UPDATE task_queue
                    SET status = 'failed',
                        completed_at = ?,
                        result_message = ?
                    WHERE id = ?
                ''', (beijing_time, error, task_id))

                # 确保系统状态与任务状态一致 - 检查当前处理的任务ID
                cursor = conn.execute('''
                    SELECT current_processing_task_id FROM system_status WHERE id = 1
                ''')
                system_status = cursor.fetchone()

                # 如果系统状态显示正在处理当前任务，则重置系统状态
                if system_status and system_status[0] == task_id:
                    self.log(f"🔄 重置系统状态（任务失败）")
                    conn.execute('''
                        UPDATE system_status
                        SET current_processing_task_id = NULL,
                            current_email = NULL,
                            is_processing = FALSE,
                            last_activity = ?
                        WHERE id = 1
                    ''', (beijing_time,))

                conn.commit()

                # 更新用户提交历史状态
                self.update_user_submission_status(task_id, 'failed', error)

                self.log(f"❌ 任务 {task_id} 失败: {error}")

            # 执行失败后的处理流程
            if task_info:
                email, profile_name = task_info
                self.log(f"🔄 开始执行失败处理流程: {email} (配置: {profile_name})")

                # 1. 关闭当前浏览器
                self._handle_task_failure_cleanup(profile_name, task_id, error)

                # 2. 切换到下一个配置
                self._switch_to_next_profile_after_failure(task_id, error)

        except Exception as e:
            self.log(f"❌ 处理任务失败时出错: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")

    def _handle_task_failure_cleanup(self, profile_name, task_id, error):
        """处理任务失败后的清理工作"""
        try:
            self.log(f"🧹 清理失败任务的浏览器: {profile_name}")

            # 优雅关闭浏览器
            try:
                self.graceful_close_browser_after_completion(profile_name)
                self.log(f"✅ 浏览器已关闭: {profile_name}")
            except Exception as e:
                self.log(f"⚠️ 关闭浏览器时出错: {e}")
                # 强制关闭
                try:
                    self.force_close_browser_processes(profile_name)
                    self.log(f"🔨 强制关闭浏览器: {profile_name}")
                except Exception as e2:
                    self.log(f"❌ 强制关闭浏览器失败: {e2}")

            # 标记配置为可用
            self.log(f"📝 标记配置为可用: {profile_name}")

        except Exception as e:
            self.log(f"❌ 失败清理过程出错: {e}")

    def _switch_to_next_profile_after_failure(self, failed_task_id, error):
        """失败后切换到下一个配置"""
        try:
            self.log(f"🔄 准备切换到下一个配置...")

            # 更新系统状态为空闲（由于process_task的finally已经重置了is_processing，这里只需要更新数据库）
            self.update_system_idle_status()

            # 等待一段时间让系统稳定，然后继续处理
            self.root.after(2000, lambda: self._continue_processing_after_failure(failed_task_id, error))

        except Exception as e:
            self.log(f"❌ 切换配置时出错: {e}")
            # 确保系统能继续处理
            self.root.after(3000, lambda: self.update_system_idle_status())

    def _continue_processing_after_failure(self, failed_task_id, error):
        """失败后继续处理队列"""
        try:
            self.log(f"🚀 失败处理完成，系统将自动继续处理队列...")

            # 不需要手动获取下一个任务，让task_processing_loop自然处理
            # 这样可以避免重复处理和状态冲突
            self.log(f"✅ 失败处理流程完成，等待任务处理循环继续...")

        except Exception as e:
            self.log(f"❌ 继续处理队列时出错: {e}")

    def update_system_idle_status(self):
        """更新系统为空闲状态"""
        try:
            beijing_time = self.get_beijing_time()
            with sqlite3.connect('tasks.db') as conn:
                conn.execute('''
                    UPDATE system_status
                    SET current_processing_task_id = NULL,
                        current_email = NULL,
                        is_processing = FALSE,
                        last_activity = ?
                    WHERE id = 1
                ''', (beijing_time,))
                conn.commit()  # 确保立即提交
                self.log(f"🔄 系统状态已重置为空闲")
        except Exception as e:
            self.log(f"❌ 更新系统空闲状态失败: {e}")

    def check_system_status_consistency(self):
        """按需检查系统状态一致性，只在必要时连接数据库"""
        # 添加检查间隔，避免频繁调用
        if not hasattr(self, '_last_consistency_check'):
            self._last_consistency_check = 0

        current_time = time.time()
        if current_time - self._last_consistency_check < 1800:  # 30分钟内不重复检查
            return

        try:
            # 快速连接，检查后立即断开
            with get_sqlite_connection('tasks.db', timeout=10) as conn:

                # 使用单个查询获取所需信息
                cursor = conn.execute('''
                    SELECT
                        (SELECT is_processing FROM system_status WHERE id = 1) as sys_processing,
                        (SELECT current_processing_task_id FROM system_status WHERE id = 1) as sys_task_id,
                        (SELECT current_email FROM system_status WHERE id = 1) as sys_email,
                        (SELECT COUNT(*) FROM task_queue WHERE status = "processing") as processing_count,
                        (SELECT id FROM task_queue WHERE status = "processing" LIMIT 1) as first_processing_id,
                        (SELECT email FROM task_queue WHERE status = "processing" LIMIT 1) as first_processing_email
                ''')
                status_info = cursor.fetchone()

                # 安全地获取状态信息
                sys_processing = self.safe_get_db_value(status_info, 'sys_processing') or self.safe_get_db_value(status_info, 0)
                processing_count = self.safe_get_db_value(status_info, 'processing_count') or self.safe_get_db_value(status_info, 3) or 0
                first_task_id = self.safe_get_db_value(status_info, 'first_processing_id') or self.safe_get_db_value(status_info, 4)
                first_task_email = self.safe_get_db_value(status_info, 'first_processing_email') or self.safe_get_db_value(status_info, 5)
                needs_fix = False

                # 检查一致性
                if sys_processing and processing_count == 0:
                    # 系统显示正在处理，但没有处理中的任务
                    self.log(f"⚠️ 检测到系统状态不一致，自动修复...")
                    beijing_time = self.get_beijing_time()
                    conn.execute('''
                        UPDATE system_status
                        SET current_processing_task_id = NULL,
                            current_email = NULL,
                            is_processing = FALSE,
                            last_activity = ?
                        WHERE id = 1
                    ''', (beijing_time,))
                    conn.commit()
                    self.log(f"✅ 系统状态已自动修复")
                    needs_fix = True

                elif not sys_processing and processing_count > 0:
                    # 系统显示空闲，但有处理中的任务
                    if first_task_id and first_task_email:
                        self.log(f"⚠️ 检测到遗漏的处理中任务，更新系统状态...")
                        beijing_time = self.get_beijing_time()
                        conn.execute('''
                            UPDATE system_status
                            SET current_processing_task_id = ?,
                                current_email = ?,
                                is_processing = TRUE,
                                last_activity = ?
                            WHERE id = 1
                        ''', (first_task_id, first_task_email, beijing_time))
                        conn.commit()
                        self.log(f"✅ 系统状态已更新为处理中: {first_task_email}")
                        needs_fix = True

                # 更新检查时间
                self._last_consistency_check = current_time

                # 如果修复了状态，触发UI更新
                if needs_fix:
                    self.root.after(0, self._update_basic_status_only)

        except sqlite3.OperationalError as e:
            if "database is locked" not in str(e):
                self.log(f"⚠️ 状态一致性检查失败: {e}")
        except Exception as e:
            self.log(f"⚠️ 状态一致性检查异常: {e}")

    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        # 在主线程中更新UI
        self.root.after(0, lambda: self._update_log(log_message))

    def _update_log(self, message):
        """更新日志显示"""
        self.log_text.insert(tk.END, message)
        self.log_text.see(tk.END)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("🗑️ 日志已清空")

    def clear_task_history(self):
        """清空任务历史（先保存到Excel）- 异步优化版本"""
        # 防重复点击检查
        if hasattr(self, '_clearing_history') and self._clearing_history:
            self.log("⚠️ 正在清空历史中，请稍候...")
            return

        try:
            # 确认对话框
            result = messagebox.askyesno(
                "确认清空",
                "确定要清空所有任务历史吗？\n\n清空前会将历史数据保存到 history.xlsx 文件中。",
                icon='warning'
            )

            if not result:
                return

            self._clearing_history = True
            self.log("📋 开始清空任务历史...")

            def _clear_in_background():
                try:
                    # 1. 从数据库获取所有任务历史
                    with get_sqlite_connection('tasks.db', timeout=30) as conn:
                        cursor = conn.execute('''
                            SELECT * FROM task_queue
                            ORDER BY created_at DESC
                        ''')
                        tasks = cursor.fetchall()

                    if not tasks:
                        self.root.after(0, lambda: self.log("ℹ️ 没有任务历史需要清空"))
                        return

                    # 2. 保存到Excel文件
                    self.root.after(0, lambda: self.log("💾 正在保存历史数据到Excel..."))
                    self.save_tasks_to_excel(tasks)

                    # 3. 清空数据库中的任务历史
                    self.root.after(0, lambda: self.log("🗑️ 正在清空数据库..."))
                    with get_sqlite_connection('tasks.db', timeout=30) as conn:
                        cursor = conn.execute('DELETE FROM task_queue')
                        deleted_count = cursor.rowcount

                        # 重置自增ID
                        conn.execute('DELETE FROM sqlite_sequence WHERE name="task_queue"')

                    # 4. 在主线程中刷新界面显示
                    self.root.after(0, self.refresh_task_display)

                    # 5. 显示成功消息
                    self.root.after(0, lambda: self.log(f"✅ 任务历史清空完成！共清空 {deleted_count} 条记录"))
                    self.root.after(0, lambda: self.log("📊 历史数据已保存到 history.xlsx"))
                    self.root.after(0, lambda: messagebox.showinfo("清空完成", f"成功清空 {deleted_count} 条任务历史！\n历史数据已保存到 history.xlsx"))

                except Exception as e:
                    error_msg = f"清空任务历史失败: {str(e)}"
                    self.root.after(0, lambda: self.log(f"❌ {error_msg}"))
                    self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                finally:
                    self._clearing_history = False

            # 在后台线程中执行清空操作
            import threading
            threading.Thread(target=_clear_in_background, daemon=True).start()

        except Exception as e:
            self._clearing_history = False
            error_msg = f"清空任务历史失败: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def save_tasks_to_excel(self, tasks, prefix="history"):
        """将任务历史保存到Excel文件"""
        try:
            import pandas as pd
            from datetime import datetime

            # 准备数据
            data = []
            for task in tasks:
                # 状态映射
                status_map = {
                    'waiting': '等待处理',
                    'processing': '处理中',
                    'completed': '已完成',
                    'failed': '失败'
                }

                data.append({
                    '任务ID': task['id'],
                    '用户令牌': task['user_token'],
                    '邮箱地址': task['email'],
                    '状态': status_map.get(task['status'], task['status']),
                    '队列位置': task['queue_position'],
                    '创建时间': task['created_at'],
                    '开始时间': task['started_at'],
                    '完成时间': task['completed_at'],
                    '结果消息': task['result_message'],
                    '处理详情': task['processing_details']
                })

            # 创建DataFrame
            df = pd.DataFrame(data)

            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{prefix}_{timestamp}.xlsx"

            # 保存到Excel
            df.to_excel(filename, index=False, sheet_name='任务历史')

            self.log(f"📊 任务历史已保存到: {filename}")

        except ImportError:
            # 如果没有pandas，使用openpyxl直接写入
            self.save_tasks_to_excel_openpyxl(tasks, prefix)
        except Exception as e:
            raise Exception(f"保存Excel文件失败: {str(e)}")

    def save_tasks_to_excel_openpyxl(self, tasks, prefix="history"):
        """使用openpyxl保存任务历史到Excel（备用方法）"""
        try:
            from openpyxl import Workbook
            from datetime import datetime

            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "任务历史"

            # 设置表头
            headers = ['任务ID', '用户令牌', '邮箱地址', '状态', '队列位置',
                      '创建时间', '开始时间', '完成时间', '结果消息', '处理详情']
            ws.append(headers)

            # 状态映射
            status_map = {
                'waiting': '等待处理',
                'processing': '处理中',
                'completed': '已完成',
                'failed': '失败'
            }

            # 添加数据
            for task in tasks:
                row = [
                    task['id'],
                    task['user_token'],
                    task['email'],
                    status_map.get(task['status'], task['status']),
                    task['queue_position'],
                    task['created_at'],
                    task['started_at'],
                    task['completed_at'],
                    task['result_message'],
                    task['processing_details']
                ]
                ws.append(row)

            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{prefix}_{timestamp}.xlsx"

            # 保存文件
            wb.save(filename)

            self.log(f"📊 任务历史已保存到: {filename}")

        except Exception as e:
            raise Exception(f"保存Excel文件失败: {str(e)}")

    def show_restore_dialog(self):
        """显示恢复配置对话框"""
        try:
            # 获取old文件夹中的配置
            old_profiles = []
            if os.path.exists(self.config_manager.old_profiles_dir):
                old_items = os.listdir(self.config_manager.old_profiles_dir)
                old_profiles = [item for item in old_items
                              if os.path.isdir(os.path.join(self.config_manager.old_profiles_dir, item))]

            if not old_profiles:
                messagebox.showinfo("提示", "old文件夹中没有可恢复的配置")
                return

            # 创建选择对话框
            dialog = tk.Toplevel(self.root)
            dialog.title("恢复配置")
            dialog.geometry("400x300")
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 50,
                self.root.winfo_rooty() + 50
            ))

            ttk.Label(dialog, text="选择要恢复的配置:", font=("Arial", 12)).pack(pady=10)

            # 配置列表
            listbox_frame = ttk.Frame(dialog)
            listbox_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

            listbox = tk.Listbox(listbox_frame)
            scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=listbox.yview)
            listbox.configure(yscrollcommand=scrollbar.set)

            for profile in old_profiles:
                listbox.insert(tk.END, profile)

            listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 按钮
            button_frame = ttk.Frame(dialog)
            button_frame.pack(pady=10)

            def restore_selected():
                selection = listbox.curselection()
                if not selection:
                    messagebox.showwarning("警告", "请选择要恢复的配置")
                    return

                profile_name = listbox.get(selection[0])

                if self.config_manager.restore_profile_from_old(profile_name):
                    self.log(f"✅ 配置恢复成功: {profile_name}")
                    self.load_profiles()  # 刷新配置列表
                    dialog.destroy()
                    messagebox.showinfo("成功", f"配置 {profile_name} 恢复成功")
                else:
                    messagebox.showerror("错误", f"配置 {profile_name} 恢复失败")

            ttk.Button(button_frame, text="恢复", command=restore_selected).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            self.log(f"❌ 显示恢复对话框失败")
            messagebox.showerror("错误", "显示恢复对话框失败")

    def open_frontend(self):
        """打开前端测试界面"""
        try:
            import subprocess
            import os

            # 获取当前脚本目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            frontend_path = os.path.join(current_dir, "fronttest.py")

            # 检查前端文件是否存在
            if not os.path.exists(frontend_path):
                messagebox.showerror("错误", f"前端文件不存在: {frontend_path}")
                return

            # 启动前端程序
            subprocess.Popen([
                "python", frontend_path
            ], cwd=current_dir)

            self.log("🖥️ 前端测试界面已启动")
            messagebox.showinfo("成功", "前端测试界面已启动")

        except Exception as e:
            self.log(f"❌ 启动前端失败")
            messagebox.showerror("错误", f"启动前端失败: {str(e)}")

    def open_excel_file(self):
        """打开Excel配置统计文件"""
        try:
            import subprocess
            import os

            excel_file = self.usage_tracker.excel_file

            if not os.path.exists(excel_file):
                self.log(f"❌ Excel文件不存在: {excel_file}")
                return

            # 使用默认程序打开Excel文件
            os.startfile(excel_file)

            self.log("📊 Excel配置统计文件已打开")

        except Exception as e:
            self.log(f"❌ 打开Excel文件失败: {str(e)}")

    def reset_all_profiles(self):
        """重置所有配置为未使用状态"""
        try:
            result = messagebox.askyesno("确认", "确定要重置所有配置为未使用状态吗？\n这将清除所有使用记录。")

            if result:
                reset_count = self.usage_tracker.reset_all_profiles()

                if reset_count > 0:
                    self.log(f"✅ 重置了 {reset_count} 个配置")

                    # 重新加载配置 - 这会更新可用配置列表
                    self.load_profiles()

                    messagebox.showinfo("成功", f"成功重置了 {reset_count} 个配置为未使用状态\n现在所有配置都可用了")
                else:
                    messagebox.showwarning("提示", "没有配置需要重置")

        except Exception as e:
            self.log(f"❌ 重置配置失败")
            messagebox.showerror("错误", f"重置配置失败: {str(e)}")

    def handle_submit_task(self):
        """处理任务提交请求（需要用户认证）"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            # 验证用户认证
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)
            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            # 检查用户剩余次数
            if not self.auth_manager.check_user_quota(user_info['id']):
                return jsonify({'success': False, 'message': '剩余次数不足，请充值后再试'}), 403

            data = request.get_json()
            email = data.get('email', '').strip()

            # 验证邮箱
            if not email or not self.validate_email(email):
                return jsonify({'success': False, 'message': '邮箱格式无效'}), 400

            # 生成用户标识（包含用户ID）
            user_token = f"user_{user_info['id']}_{int(time.time())}"

            # 添加到队列
            task_id, queue_position = self.add_task_to_queue(email, user_token, user_info['id'])

            # 保存到用户提交历史
            self.save_user_submission(user_info['id'], email, task_id, queue_position, request.remote_addr)

            self.log(f"📧 用户 {user_info['username']} 提交请求: {email}")

            return jsonify({
                'success': True,
                'task_id': task_id,
                'queue_position': queue_position,
                'message': f'任务已提交，排队第{queue_position}位',
                'user_quota': user_info['time_quota']
            }), 200

        except Exception as e:
            self.log(f"❌ 处理提交请求失败: {e}")
            return jsonify({'success': False, 'message': '服务器内部错误'}), 500

    def handle_get_task_status(self, task_id):
        """处理任务状态查询（只能查询自己的任务）"""
        try:
            # 验证用户认证
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)
            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            with sqlite3.connect('tasks.db') as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute('SELECT * FROM task_queue WHERE id = ? AND user_id = ?', (task_id, user_info['id']))
                task = cursor.fetchone()

                if not task:
                    return jsonify({'success': False, 'message': '任务不存在或无权访问'}), 404

                # 转换状态名称以匹配前端期望
                status_map = {
                    'waiting': 'pending',
                    'processing': 'processing',
                    'completed': 'success',
                    'failed': 'failed'
                }

                # 格式化时间为北京时间
                created_at_formatted = self.format_beijing_time(task['created_at']) if task['created_at'] else None
                completed_at_formatted = self.format_beijing_time(task['completed_at']) if task['completed_at'] else None

                return jsonify({
                    'success': True,
                    'task_id': task_id,
                    'email': task['email'],
                    'status': status_map.get(task['status'], task['status']),
                    'queue_position': task['queue_position'],
                    'created_at': created_at_formatted,
                    'completed_at': completed_at_formatted,
                    'result_message': task['result_message']
                }), 200

        except Exception as e:
            return jsonify({'success': False, 'message': '查询失败'}), 500

    def handle_get_system_status(self):
        """处理系统状态查询（公开接口，不需要认证）"""
        try:
            with sqlite3.connect('tasks.db') as conn:
                conn.row_factory = sqlite3.Row

                # 获取系统状态
                cursor = conn.execute('SELECT * FROM system_status WHERE id = 1')
                system_status = cursor.fetchone()

                # 获取队列统计（所有用户的总数）
                cursor = conn.execute('SELECT COUNT(*) as count FROM task_queue WHERE status = "waiting"')
                queue_length = cursor.fetchone()['count']

                # 获取可用配置数量（缓存结果，避免频繁计算）
                if not hasattr(self, '_cached_available_count') or not hasattr(self, '_last_profile_check') or \
                   time.time() - self._last_profile_check > 60:  # 1分钟缓存
                    available_profiles = self.get_available_profiles()
                    self._cached_available_count = len(available_profiles)
                    self._last_profile_check = time.time()

                available_count = self._cached_available_count

                # 不显示具体的邮箱地址，保护隐私
                current_email = None
                if system_status['current_email']:
                    email = system_status['current_email']
                    if '@' in email:
                        local, domain = email.split('@', 1)
                        current_email = f"{local[:2]}***@{domain}"
                    else:
                        current_email = "***"

                return jsonify({
                    'success': True,
                    'is_processing': bool(system_status['is_processing']),
                    'current_email': current_email,
                    'started_at': system_status['started_at'],
                    'queue_length': queue_length,
                    'available_profiles': available_count
                }), 200

        except Exception as e:
            return jsonify({'success': False, 'message': '获取状态失败'}), 500

    def handle_get_queue_list(self):
        """处理队列列表查询（只返回当前用户的任务）"""
        try:
            # 验证用户认证
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)
            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            with sqlite3.connect('tasks.db') as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute('''
                    SELECT id, email, status, queue_position, created_at, completed_at, result_message
                    FROM task_queue
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                    LIMIT 50
                ''', (user_info['id'],))

                tasks = cursor.fetchall()
                queue_list = []

                for task in tasks:
                    # 转换状态名称
                    status_map = {
                        'waiting': 'pending',
                        'processing': 'processing',
                        'completed': 'success',
                        'failed': 'failed'
                    }

                    # 格式化时间为北京时间
                    created_at_formatted = self.format_beijing_time(task['created_at']) if task['created_at'] else None
                    completed_at_formatted = self.format_beijing_time(task['completed_at']) if task['completed_at'] else None

                    queue_list.append({
                        'id': task['id'],
                        'email': task['email'],  # 用户只能看到自己的邮箱
                        'status': status_map.get(task['status'], task['status']),
                        'created_at': created_at_formatted,
                        'completed_at': completed_at_formatted,
                        'message': task['result_message'] if task['result_message'] else None,
                        'error': task['result_message'] if task['status'] == 'failed' else None
                    })

                return jsonify({
                    'success': True,
                    'queue': queue_list,
                    'total_count': len(queue_list)
                }), 200

        except Exception as e:
            return jsonify({'success': False, 'message': '获取队列失败'}), 500

    # 公告功能已迁移到前端本地文件，不再需要数据库缓存
    # def load_announcements_from_database(self):
    #     """从数据库加载公告到缓存（启动时和定期刷新）"""
    #     try:
    #         self.log("📡 从数据库加载公告到缓存...")
    #
    #         # 尝试从数据库获取
    #         announcements = self.get_announcements_from_db()
    #
    #         if announcements:
    #             self.cached_announcements = announcements
    #             self.announcements_cache_time = time.time()
    #             self.log(f"✅ 成功缓存 {len(announcements)} 条公告")
    #             return True
    #         else:
    #             # 数据库连接失败时，使用默认公告
    #             self.cached_announcements = get_all_announcements()
    #             self.announcements_cache_time = time.time()
    #             self.log("⚠️ 数据库连接失败，使用默认公告")
    #             return False
    #
    #     except Exception as e:
    #         self.log(f"❌ 加载公告失败: {e}")
    #         # 确保有默认公告可用
    #         if not self.cached_announcements:
    #             self.cached_announcements = get_all_announcements()
    #             self.announcements_cache_time = time.time()
    #         return False

    # 公告功能已迁移到前端本地文件，不再需要缓存获取
    # def get_cached_announcements(self):
    #     """获取缓存的公告（带自动刷新）"""
    #     current_time = time.time()
    #
    #     # 检查是否需要刷新缓存
    #     if (not self.cached_announcements or
    #         current_time - self.announcements_cache_time > self.announcements_cache_duration):
    #
    #         # 异步刷新缓存，不阻塞当前请求
    #         threading.Thread(target=self.load_announcements_from_database, daemon=True).start()
    #
    #         # 如果没有缓存数据，同步加载一次
    #         if not self.cached_announcements:
    #             self.load_announcements_from_database()
    #
    #     return self.cached_announcements

    # 公告API已废弃，前端现在直接读取本地JSON文件
    # def handle_get_announcements(self):
    #     """处理获取公告请求（使用缓存，快速响应）"""
    #     try:
    #         # 从缓存获取公告，避免每次查询数据库
    #         announcements = self.get_cached_announcements()
    #
    #         cache_age = time.time() - self.announcements_cache_time
    #         cache_status = "fresh" if cache_age < 60 else "cached"
    #
    #         self.log(f"📢 返回缓存公告 ({len(announcements)} 条, 缓存时间: {cache_age:.0f}秒)")
    #
    #         return jsonify({
    #             'success': True,
    #             'data': announcements,
    #             'count': len(announcements),
    #             'message': f'成功获取 {len(announcements)} 条公告',
    #             'source': 'cached',
    #             'cache_age': int(cache_age),
    #             'cache_status': cache_status
    #         }), 200
    #
    #     except Exception as e:
    #         self.log(f"❌ 处理公告请求失败: {e}")
    #         return jsonify({
    #             'success': False,
    #             'message': '获取公告失败',
    #             'data': []
    #         }), 500



    def handle_auth_check(self):
        """处理认证检查请求"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            data = request.get_json()
            token = data.get('token', '')

            if not token:
                return jsonify({
                    'success': False,
                    'authenticated': False,
                    'message': '缺少认证令牌'
                }), 400

            # 验证令牌
            user_info = self.auth_manager.require_auth(token)

            if user_info:
                return jsonify({
                    'success': True,
                    'authenticated': True,
                    'user': {
                        'id': user_info['id'],
                        'username': user_info['username'],
                        'time_quota': user_info.get('time_quota', 0)
                    }
                }), 200
            else:
                return jsonify({
                    'success': False,
                    'authenticated': False,
                    'message': '无效的认证令牌'
                }), 401

        except Exception as e:
            self.log(f"❌ 认证检查失败: {e}")
            return jsonify({
                'success': False,
                'authenticated': False,
                'message': '认证检查失败'
            }), 500

    # 公告管理API已废弃，前端现在直接读取本地JSON文件
    # def handle_refresh_announcements(self):
    #     """处理管理员刷新公告缓存请求"""
    #     try:
    #         # 验证管理员权限（可选，根据需要实现）
    #         # auth_header = request.headers.get('Authorization')
    #         # user_info = self.auth_manager.require_auth(auth_header)
    #         # if not user_info or user_info.get('role') != 'admin':
    #         #     return jsonify({'success': False, 'message': '需要管理员权限'}), 403
    #
    #         # 强制刷新公告缓存
    #         old_count = len(self.cached_announcements)
    #         success = self.load_announcements_from_database()
    #         new_count = len(self.cached_announcements)
    #
    #         if success:
    #             return jsonify({
    #                 'success': True,
    #                 'message': f'公告缓存刷新成功',
    #                 'old_count': old_count,
    #                 'new_count': new_count,
    #                 'cache_time': time.time()
    #             }), 200
    #         else:
    #             return jsonify({
    #                 'success': False,
    #                 'message': '公告缓存刷新失败，使用默认公告',
    #                 'old_count': old_count,
    #                 'new_count': new_count
    #             }), 200  # 仍然返回200，因为有默认公告
    #
    #     except Exception as e:
    #         self.log(f"❌ 刷新公告缓存失败: {e}")
    #         return jsonify({
    #             'success': False,
    #             'message': '刷新公告缓存失败'
    #         }), 500

    def validate_email(self, email):
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def add_task_to_queue(self, email, user_token, user_id=None):
        """添加任务到队列"""
        with get_sqlite_connection('tasks.db') as conn:
            # 检查是否有相同邮箱的待处理任务
            cursor = conn.execute('''
                SELECT COUNT(*) FROM task_queue
                WHERE email = ? AND status IN ('waiting', 'processing')
            ''', (email,))

            existing_count = cursor.fetchone()[0]
            if existing_count > 0:
                raise Exception(f"邮箱 {email} 已在处理队列中，请勿重复提交")

            # 获取当前最大排队位置
            cursor = conn.execute('''
                SELECT COALESCE(MAX(queue_position), 0) + 1
                FROM task_queue
                WHERE status = 'waiting'
            ''')
            next_position = cursor.fetchone()[0]

            # 插入新任务
            beijing_time = self.get_beijing_time()
            cursor = conn.execute('''
                INSERT INTO task_queue (user_token, user_id, email, queue_position, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_token, user_id, email, next_position, beijing_time))

            task_id = cursor.lastrowid

            # 触发任务检查事件，立即处理新任务
            self.trigger_task_check()

            # 任务提交后自动刷新任务列表
            self.root.after(1000, self.refresh_task_display)  # 自动刷新任务列表
            self.log("✅ 新任务已提交，已触发任务处理")

            return task_id, next_position

    def update_status_display(self):
        """更新状态显示"""
        try:
            with sqlite3.connect('tasks.db') as conn:
                conn.row_factory = sqlite3.Row

                # 获取系统状态
                cursor = conn.execute('SELECT * FROM system_status WHERE id = 1')
                system_status = cursor.fetchone()

                # 更新当前状态显示
                if system_status['is_processing']:
                    self.current_status_var.set(f"🔄 处理中: {system_status['current_email']}")
                else:
                    self.current_status_var.set("🟢 空闲中")

                # 获取队列统计
                cursor = conn.execute('''
                    SELECT
                        COUNT(CASE WHEN status = 'waiting' THEN 1 END) as waiting,
                        COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
                    FROM task_queue
                    WHERE DATE(created_at) = DATE('now')
                ''')

                stats = cursor.fetchone()
                self.queue_stats_var.set(f"等待: {stats['waiting']}, 处理中: {stats['processing']}, 完成: {stats['completed']}, 失败: {stats['failed']}")

                # 更新任务列表
                self.refresh_task_display()

        except Exception as e:
            self.log(f"❌ 状态更新失败: {e}")

    def refresh_queue_display(self):
        """刷新队列显示（兼容性方法）"""
        self.refresh_task_display()

    def refresh_task_display(self):
        """刷新任务显示（异步优化版本）"""
        def _refresh_in_background():
            try:
                # 获取过滤条件
                filter_type = self.task_filter_var.get()

                # 从数据库获取任务（限制数量，避免大量数据导致卡顿）
                with get_sqlite_connection('tasks.db', timeout=5) as conn:

                    if filter_type == "all":
                        cursor = conn.execute('''
                            SELECT * FROM task_queue
                            ORDER BY created_at DESC
                            LIMIT 200
                        ''')
                    elif filter_type == "pending":
                        cursor = conn.execute('''
                            SELECT * FROM task_queue
                            WHERE status IN ('waiting', 'processing')
                            ORDER BY created_at DESC
                            LIMIT 100
                        ''')
                    elif filter_type == "completed":
                        cursor = conn.execute('''
                            SELECT * FROM task_queue
                            WHERE status IN ('completed', 'failed')
                            ORDER BY created_at DESC
                            LIMIT 200
                        ''')

                    tasks = cursor.fetchall()

                # 在主线程中更新UI
                self.root.after(0, lambda: self._update_task_tree(tasks))

            except Exception as e:
                self.log(f"❌ 刷新任务显示失败: {e}")
                self.root.after(0, lambda: self._update_task_tree([]))

        # 在后台线程中执行数据库查询
        import threading
        threading.Thread(target=_refresh_in_background, daemon=True).start()

    def _update_task_tree(self, tasks):
        """在主线程中更新任务树（UI操作）"""
        try:
            # 清空现有项目
            for item in self.tasks_tree.get_children():
                self.tasks_tree.delete(item)

            # 状态映射
            status_map = {
                'waiting': '⏳ 等待',
                'processing': '🔄 处理中',
                'completed': '✅ 成功',
                'failed': '❌ 失败'
            }

            # 统计信息
            total_count = 0
            waiting_count = 0
            processing_count = 0
            completed_count = 0
            failed_count = 0

            # 批量添加任务到列表（优化性能）
            items_to_insert = []
            for task in tasks:
                status = task['status']
                status_display = status_map.get(status, status)

                # 格式化时间
                created_time = task['created_at']
                if created_time:
                    try:
                        # 使用北京时间格式化
                        formatted_time = self.format_beijing_time(created_time)
                        # 只显示月-日 时:分
                        dt = datetime.fromisoformat(formatted_time)
                        created_display = dt.strftime('%m-%d %H:%M')
                    except:
                        created_display = created_time[:16] if created_time else ''
                else:
                    created_display = ''

                # 处理结果
                result = task['result_message'] or ''
                if len(result) > 30:
                    result = result[:30] + '...'

                # 准备插入数据
                items_to_insert.append((
                    task['id'],
                    task['email'],
                    status_display,
                    created_display,
                    result
                ))

                # 统计
                total_count += 1
                if status == 'waiting':
                    waiting_count += 1
                elif status == 'processing':
                    processing_count += 1
                elif status == 'completed':
                    completed_count += 1
                elif status == 'failed':
                    failed_count += 1

            # 批量插入到树视图（减少UI更新次数）并应用颜色标记
            for i, item_data in enumerate(items_to_insert):
                # 获取对应的任务状态
                task = tasks[i]
                status = task['status']

                # 插入项目
                item_id = self.tasks_tree.insert('', 'end', values=item_data)

                # 根据状态应用颜色标记
                if status == 'completed':
                    self.tasks_tree.item(item_id, tags=('completed',))
                elif status == 'failed':
                    self.tasks_tree.item(item_id, tags=('failed',))
                elif status == 'processing':
                    self.tasks_tree.item(item_id, tags=('processing',))
                elif status == 'waiting':
                    self.tasks_tree.item(item_id, tags=('pending',))

            # 更新统计信息
            filter_type = self.task_filter_var.get()
            stats_text = f"总计: {total_count} | 等待: {waiting_count} | 处理中: {processing_count} | 成功: {completed_count} | 失败: {failed_count}"
            self.task_stats_var.set(stats_text)

            self.log(f"📊 任务显示已刷新 ({filter_type}): {total_count} 条记录")

        except Exception as e:
            self.log(f"❌ 更新任务树失败: {e}")

    def export_current_tasks(self):
        """导出当前显示的任务"""
        try:
            filter_type = self.task_filter_var.get()
            filter_names = {
                "all": "所有任务",
                "pending": "待处理任务",
                "completed": "已处理任务"
            }

            # 获取当前显示的任务
            with get_sqlite_connection('tasks.db') as conn:

                if filter_type == "all":
                    cursor = conn.execute('SELECT * FROM task_queue ORDER BY created_at DESC')
                elif filter_type == "pending":
                    cursor = conn.execute('''
                        SELECT * FROM task_queue
                        WHERE status IN ('waiting', 'processing')
                        ORDER BY created_at DESC
                    ''')
                elif filter_type == "completed":
                    cursor = conn.execute('''
                        SELECT * FROM task_queue
                        WHERE status IN ('completed', 'failed')
                        ORDER BY created_at DESC
                    ''')

                tasks = cursor.fetchall()

            if not tasks:
                messagebox.showinfo("提示", f"没有{filter_names[filter_type]}可导出")
                return

            # 保存到Excel
            self.save_tasks_to_excel(tasks, f"{filter_names[filter_type]}")

        except Exception as e:
            self.log(f"❌ 导出任务失败: {e}")
            messagebox.showerror("错误", f"导出任务失败: {str(e)}")

    def clear_completed_tasks(self):
        """清空已完成的任务（异步优化版本）"""
        # 防重复点击检查
        if hasattr(self, '_clearing_completed') and self._clearing_completed:
            self.log("⚠️ 正在清空已完成任务中，请稍候...")
            return

        try:
            # 确认对话框
            result = messagebox.askyesno(
                "确认清空",
                "确定要清空所有已完成的任务吗？\n\n这将删除所有状态为'成功'和'失败'的任务记录。",
                icon='warning'
            )

            if not result:
                return

            self._clearing_completed = True
            self.log("🗑️ 开始清空已完成任务...")

            def _clear_completed_in_background():
                try:
                    # 删除已完成的任务
                    with get_sqlite_connection('tasks.db', timeout=10) as conn:
                        cursor = conn.execute('''
                            DELETE FROM task_queue
                            WHERE status IN ('completed', 'failed')
                        ''')
                        deleted_count = cursor.rowcount

                    # 在主线程中刷新显示
                    self.root.after(0, self.refresh_task_display)

                    # 显示结果
                    self.root.after(0, lambda: self.log(f"🗑️ 已清空 {deleted_count} 条已完成任务"))
                    self.root.after(0, lambda: messagebox.showinfo("清空完成", f"成功清空 {deleted_count} 条已完成任务！"))

                except Exception as e:
                    error_msg = f"清空已完成任务失败: {str(e)}"
                    self.root.after(0, lambda: self.log(f"❌ {error_msg}"))
                    self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                finally:
                    self._clearing_completed = False

            # 在后台线程中执行清空操作
            import threading
            threading.Thread(target=_clear_completed_in_background, daemon=True).start()

        except Exception as e:
            self._clearing_completed = False
            error_msg = f"清空已完成任务失败: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def open_current_profile_browser(self):
        """打开当前配置的浏览器（与main.py保持一致的方法）"""
        current_profile = self.profile_var.get()
        if not current_profile:
            self.log("⚠️ 请先选择一个配置")
            messagebox.showinfo("提示", "请先选择一个配置")
            return

        # 防重复点击检查
        if hasattr(self, '_browser_launching') and self._browser_launching:
            self.log("⚠️ 浏览器正在启动中，请稍候...")
            return

        try:
            self._browser_launching = True
            self.log(f"🌐 正在启动浏览器: {current_profile}")

            def launch_and_open():
                try:
                    # 检查配置是否存在
                    profile = self.config_manager.get_profile(current_profile)
                    if not profile:
                        raise ValueError(f"配置 {current_profile} 不存在")

                    self.log(f"🚀 启动配置: {current_profile}")
                    self.log(f"📁 配置路径: {profile.get('path', 'N/A')}")
                    self.log(f"🎭 Stealth模式: {profile.get('stealth_enabled', False)}")

                    # 检查是否已经在运行
                    if current_profile in self.browser_manager.running_browsers:
                        # 验证是否真的在运行
                        is_running = self.browser_manager._comprehensive_browser_check(
                            self.browser_manager.running_browsers[current_profile]
                        )
                        if is_running:
                            self.log(f"✅ 浏览器已在运行，直接使用: {current_profile}")
                            # 浏览器已运行时不需要重新加载配置
                            return
                        else:
                            self.log(f"🧹 清理无效的浏览器记录: {current_profile}")
                            del self.browser_manager.running_browsers[current_profile]

                    # 启动浏览器（会自动恢复上次会话）
                    browser_info = self.browser_manager.launch_browser(current_profile)

                    if browser_info:
                        stealth_mode = browser_info.get('stealth_mode', False)
                        pid = browser_info.get('pid', 'N/A')

                        self.log(f"✅ 浏览器启动成功: {current_profile}")
                        self.log(f"🔧 模式: {'Stealth' if stealth_mode else '标准'}")
                        self.log(f"🆔 PID: {pid}")

                        # 更新UI状态
                        self.root.after(0, lambda: self.load_profiles())

                        # 对于新启动的浏览器，等待一段时间让会话恢复完成
                        if not browser_info.get('existing_instance', False):
                            self.log(f"⏳ 等待会话恢复完成...")
                            # 给Chrome更多时间来恢复会话
                            self.root.after(5000, lambda: self.open_target_website(current_profile))
                        else:
                            self.log(f"🔄 复用现有实例，会话应该已经存在")
                            self.root.after(3000, lambda: self.open_target_website(current_profile))
                    else:
                        self.log("❌ 浏览器启动失败")
                        self.root.after(0, lambda: messagebox.showerror("错误", "浏览器启动失败"))

                except Exception as e:
                    error_msg = str(e)
                    self.log(f"❌ 启动浏览器失败: {error_msg}")
                    self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", f"启动浏览器失败: {msg}"))
                finally:
                    # 重置启动标志
                    self._browser_launching = False

            # 在后台线程中启动浏览器，避免UI阻塞
            self.log(f"🚀 开始在后台启动浏览器: {current_profile}")

            # 使用daemon线程，确保主程序退出时线程也会退出
            import threading
            thread = threading.Thread(target=launch_and_open, daemon=True)
            thread.start()

            self.log(f"✅ 启动线程已创建，浏览器正在后台启动...")

        except Exception as e:
            self._browser_launching = False
            self.log(f"❌ 启动浏览器异常: {e}")
            messagebox.showerror("错误", f"启动浏览器异常: {e}")

    def open_target_website(self, profile_name):
        """智能打开目标网站（保护登录状态，与main.py保持一致）"""
        try:
            target_url = "https://app.augmentcode.com/account/team"

            # 首先检查当前页面状态
            driver = self.browser_manager.get_automation_driver(profile_name)
            if not driver:
                self.log(f"⚠️ 无法获取浏览器驱动: {profile_name}")
                return

            current_status = driver.execute_script("""
                const currentUrl = window.location.href;
                const isAugmentSite = currentUrl.includes('augmentcode.com');
                const isLoginPage = currentUrl.includes('login') ||
                                  document.querySelector('input[type="password"]') !== null;
                const isTeamPage = currentUrl.includes('/account/team');

                return {
                    currentUrl: currentUrl,
                    isAugmentSite: isAugmentSite,
                    isLoginPage: isLoginPage,
                    isTeamPage: isTeamPage,
                    pageTitle: document.title
                };
            """)

            if current_status:
                current_url = current_status.get('currentUrl', '')
                is_augment_site = current_status.get('isAugmentSite', False)
                is_login_page = current_status.get('isLoginPage', False)
                is_team_page = current_status.get('isTeamPage', False)

                self.log(f"🔍 当前页面状态: {current_url}")

                # 如果已经在团队页面，不需要导航
                if is_team_page:
                    self.log("✅ 已在团队页面，无需导航")
                    return

                # 如果在登录页面，不强制导航（让用户自己登录）
                if is_login_page:
                    self.log("⚠️ 当前在登录页面，请手动登录后使用")
                    return

                # 如果在Augment网站的其他页面，保持当前页面
                if is_augment_site and not is_team_page:
                    self.log("🔄 检测到已在Augment网站，保持当前页面")
                    return

            # 只有在非Augment网站或新打开的浏览器时才导航
            self.log(f"🌐 导航到目标页面: {target_url}")

            # 使用自动化引擎打开网页
            if hasattr(self, 'automation_engine') and self.automation_engine:
                self.automation_engine.navigate_to_url(profile_name, target_url)
                self.log(f"✅ 已导航到: {target_url}")
            else:
                # 如果没有自动化引擎，尝试直接操作浏览器
                driver.get(target_url)
                self.log(f"✅ 已导航到: {target_url}")

        except Exception as e:
            self.log(f"❌ 打开目标网站失败: {e}")
            # 即使打开网站失败，浏览器也已启动，不影响后续使用

    def show_task_context_menu(self, event):
        """显示任务右键菜单"""
        try:
            # 获取点击的项目
            item = self.tasks_tree.identify_row(event.y)
            if not item:
                return

            # 选中该项目
            self.tasks_tree.selection_set(item)

            # 获取任务信息
            task_values = self.tasks_tree.item(item, 'values')
            if not task_values:
                return

            task_id = task_values[0]
            task_email = task_values[1]
            task_status = task_values[2]

            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)

            # 查看详情
            context_menu.add_command(
                label=f"📋 查看详情 (ID: {task_id})",
                command=lambda: self.show_task_details_by_id(task_id)
            )

            context_menu.add_separator()

            # 删除任务（允许删除正在处理的任务）
            if "处理中" in task_status:
                context_menu.add_command(
                    label=f"🗑️ 强制删除任务 (正在处理)",
                    command=lambda: self.delete_task_by_id(task_id, task_email, force_delete=True)
                )
            else:
                context_menu.add_command(
                    label=f"🗑️ 删除任务",
                    command=lambda: self.delete_task_by_id(task_id, task_email)
                )

            # 如果是失败的任务，添加重试选项
            if "失败" in task_status:
                context_menu.add_separator()
                context_menu.add_command(
                    label="🔄 重新处理",
                    command=lambda: self.retry_failed_task(task_id)
                )

            # 显示菜单
            context_menu.post(event.x_root, event.y_root)

        except Exception as e:
            self.log(f"❌ 显示右键菜单失败: {e}")

    def show_task_details(self, event):
        """双击显示任务详情"""
        try:
            # 获取选中的项目
            selection = self.tasks_tree.selection()
            if not selection:
                return

            item = selection[0]
            task_values = self.tasks_tree.item(item, 'values')
            if not task_values:
                return

            task_id = task_values[0]
            self.show_task_details_by_id(task_id)

        except Exception as e:
            self.log(f"❌ 显示任务详情失败: {e}")

    def show_task_details_by_id(self, task_id):
        """根据任务ID显示详情"""
        try:
            # 从数据库获取完整任务信息
            with get_sqlite_connection('tasks.db', timeout=5) as conn:
                cursor = conn.execute('SELECT * FROM task_queue WHERE id = ?', (task_id,))
                task = cursor.fetchone()

            if not task:
                messagebox.showerror("错误", "任务不存在")
                return

            # 创建详情对话框
            dialog = tk.Toplevel(self.root)
            dialog.title(f"任务详情 - ID: {task_id}")
            dialog.geometry("500x400")
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 100,
                self.root.winfo_rooty() + 100
            ))

            # 创建详情内容
            main_frame = ttk.Frame(dialog, padding=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 任务基本信息
            info_frame = ttk.LabelFrame(main_frame, text="基本信息", padding=10)
            info_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(info_frame, text=f"任务ID: {task['id']}", font=("Arial", 10, "bold")).pack(anchor=tk.W)
            ttk.Label(info_frame, text=f"邮箱地址: {task['email']}").pack(anchor=tk.W, pady=2)
            ttk.Label(info_frame, text=f"状态: {task['status']}").pack(anchor=tk.W, pady=2)
            ttk.Label(info_frame, text=f"队列位置: {task['queue_position'] or 'N/A'}").pack(anchor=tk.W, pady=2)

            # 时间信息
            time_frame = ttk.LabelFrame(main_frame, text="时间信息", padding=10)
            time_frame.pack(fill=tk.X, pady=(0, 10))

            created_time = self.format_beijing_time(task['created_at']) if task['created_at'] else 'N/A'
            started_time = self.format_beijing_time(task['started_at']) if task['started_at'] else 'N/A'
            completed_time = self.format_beijing_time(task['completed_at']) if task['completed_at'] else 'N/A'

            ttk.Label(time_frame, text=f"提交时间: {created_time}").pack(anchor=tk.W, pady=2)
            ttk.Label(time_frame, text=f"开始时间: {started_time}").pack(anchor=tk.W, pady=2)
            ttk.Label(time_frame, text=f"完成时间: {completed_time}").pack(anchor=tk.W, pady=2)

            # 处理结果
            result_frame = ttk.LabelFrame(main_frame, text="处理结果", padding=10)
            result_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            result_text = scrolledtext.ScrolledText(result_frame, height=8, width=50)
            result_text.pack(fill=tk.BOTH, expand=True)

            result_message = task['result_message'] or '暂无处理结果'
            processing_details = task['processing_details'] or ''

            result_content = f"处理结果:\n{result_message}\n\n"
            if processing_details:
                result_content += f"处理详情:\n{processing_details}"

            result_text.insert(tk.END, result_content)
            result_text.config(state=tk.DISABLED)

            # 按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(button_frame, text="关闭", command=dialog.destroy).pack(side=tk.RIGHT)

            # 添加删除按钮（允许删除正在处理的任务）
            if task['status'] == 'processing':
                ttk.Button(button_frame, text="🗑️ 强制删除任务",
                          command=lambda: self.delete_task_from_dialog(task_id, task['email'], dialog, force_delete=True)).pack(side=tk.RIGHT, padx=(0, 10))
            else:
                ttk.Button(button_frame, text="🗑️ 删除任务",
                          command=lambda: self.delete_task_from_dialog(task_id, task['email'], dialog)).pack(side=tk.RIGHT, padx=(0, 10))

        except Exception as e:
            self.log(f"❌ 显示任务详情失败: {e}")
            messagebox.showerror("错误", f"显示任务详情失败: {str(e)}")

    def launch_main_py(self):
        """启动main.py程序"""
        try:
            import os
            import subprocess
            import sys

            # 获取main.py的路径
            main_py_path = os.path.join(os.path.dirname(__file__), 'main.py')

            # 检查文件是否存在
            if not os.path.exists(main_py_path):
                self.log("❌ main.py文件不存在")
                messagebox.showerror("错误", "main.py文件不存在")
                return

            self.log(f"🚀 正在启动main.py程序...")

            # 防重复点击检查
            if hasattr(self, '_launching_main_py') and self._launching_main_py:
                self.log("⚠️ main.py程序正在启动中，请稍候...")
                return

            self._launching_main_py = True

            def _launch_in_background():
                try:
                    # 使用Python解释器启动main.py程序（无控制台模式）
                    if sys.platform.startswith('win'):
                        # Windows系统 - 使用pythonw.exe启动，不显示控制台
                        python_exe = sys.executable

                        # 尝试使用pythonw.exe（无控制台版本）
                        if python_exe.endswith('python.exe'):
                            pythonw_exe = python_exe.replace('python.exe', 'pythonw.exe')
                            if os.path.exists(pythonw_exe):
                                python_exe = pythonw_exe
                                self.log(f"🔇 使用无控制台模式启动: {pythonw_exe}")

                        # 启动进程，隐藏窗口
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = subprocess.SW_HIDE

                        subprocess.Popen([python_exe, main_py_path],
                                       startupinfo=startupinfo,
                                       creationflags=subprocess.CREATE_NO_WINDOW)

                        self.log(f"✅ main.py程序已在后台启动（无控制台）")
                    else:
                        # Linux/macOS系统 - 在后台启动
                        subprocess.Popen([sys.executable, main_py_path],
                                       stdout=subprocess.DEVNULL,
                                       stderr=subprocess.DEVNULL)
                        self.log(f"✅ main.py程序已在后台启动")

                    self.root.after(0, lambda: self.log(f"💡 main.py程序运行中，可通过任务管理器查看"))

                except subprocess.SubprocessError as e:
                    error_msg = f"启动main.py程序失败: {str(e)}"
                    self.root.after(0, lambda: self.log(f"❌ {error_msg}"))
                    self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                except Exception as e:
                    error_msg = f"启动main.py程序失败: {str(e)}"
                    self.root.after(0, lambda: self.log(f"❌ {error_msg}"))
                    self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                finally:
                    self._launching_main_py = False

            # 在后台线程中执行启动操作
            import threading
            threading.Thread(target=_launch_in_background, daemon=True).start()

        except Exception as e:
            self._launching_main_py = False
            error_msg = f"启动main.py程序异常: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def delete_selected_task(self):
        """删除选中的任务"""
        try:
            # 获取选中的项目
            selection = self.tasks_tree.selection()
            if not selection:
                messagebox.showinfo("提示", "请先选择要删除的任务")
                return

            item = selection[0]
            task_values = self.tasks_tree.item(item, 'values')
            if not task_values:
                return

            task_id = task_values[0]
            task_email = task_values[1]
            task_status = task_values[2]

            # 检查是否是正在处理的任务
            if "处理中" in task_status:
                # 询问是否强制删除正在处理的任务
                result = messagebox.askyesno(
                    "确认强制删除",
                    f"任务正在处理中！\n\n"
                    f"强制删除将会:\n"
                    f"• 停止当前任务处理\n"
                    f"• 重置系统处理状态\n"
                    f"• 删除任务记录\n\n"
                    f"确定要强制删除吗？",
                    icon='warning'
                )
                if result:
                    self.delete_task_by_id(task_id, task_email, force_delete=True)
            else:
                self.delete_task_by_id(task_id, task_email)

        except Exception as e:
            self.log(f"❌ 删除选中任务失败: {e}")
            messagebox.showerror("错误", f"删除任务失败: {str(e)}")

    def delete_task_by_id(self, task_id, task_email, force_delete=False):
        """根据ID删除任务"""
        try:
            # 确认对话框
            if force_delete:
                confirm_msg = (
                    f"确定要强制删除正在处理的任务吗？\n\n"
                    f"任务ID: {task_id}\n"
                    f"邮箱: {task_email}\n\n"
                    f"强制删除将会:\n"
                    f"• 停止当前任务处理\n"
                    f"• 重置系统处理状态\n"
                    f"• 删除任务记录\n\n"
                    f"此操作无法撤销！"
                )
                title = "确认强制删除"
            else:
                confirm_msg = (
                    f"确定要删除以下任务吗？\n\n"
                    f"任务ID: {task_id}\n"
                    f"邮箱: {task_email}\n\n"
                    f"此操作无法撤销！"
                )
                title = "确认删除"

            result = messagebox.askyesno(title, confirm_msg, icon='warning')
            if not result:
                return

            # 防重复删除检查
            if hasattr(self, '_deleting_task') and self._deleting_task:
                self.log("⚠️ 正在删除任务中，请稍候...")
                return

            self._deleting_task = True
            self.log(f"🗑️ 开始删除任务: ID={task_id}, 邮箱={task_email}")

            def _delete_in_background():
                try:
                    # 再次检查任务状态（防止在确认期间状态改变）
                    with sqlite3.connect('tasks.db', timeout=10.0) as conn:
                        cursor = conn.execute('SELECT status FROM task_queue WHERE id = ?', (task_id,))
                        task = cursor.fetchone()

                        if not task:
                            self.root.after(0, lambda: messagebox.showinfo("提示", "任务已不存在"))
                            return

                        # 如果是正在处理的任务且不是强制删除，则拒绝删除
                        if task[0] == 'processing' and not force_delete:
                            self.root.after(0, lambda: messagebox.showwarning("警告", "任务正在处理中，无法删除！"))
                            return

                        # 如果是强制删除正在处理的任务，需要额外处理
                        if task[0] == 'processing' and force_delete:
                            self.root.after(0, lambda: self.log(f"🚨 强制删除正在处理的任务: ID={task_id}"))

                            # 停止当前任务处理
                            self.is_processing = False
                            self.current_task = None
                            self.task_start_time = None

                            # 重置系统状态
                            beijing_time = self.get_beijing_time()
                            conn.execute('''
                                UPDATE system_status
                                SET current_processing_task_id = NULL,
                                    current_email = NULL,
                                    is_processing = FALSE,
                                    last_activity = ?
                                WHERE id = 1
                            ''', (beijing_time,))

                            self.root.after(0, lambda: self.log(f"🔄 系统处理状态已重置"))

                        # 执行删除
                        cursor = conn.execute('DELETE FROM task_queue WHERE id = ?', (task_id,))
                        deleted_count = cursor.rowcount

                        if deleted_count > 0:
                            if force_delete and task[0] == 'processing':
                                self.root.after(0, lambda: self.log(f"✅ 正在处理的任务已强制删除: ID={task_id}"))
                                self.root.after(0, lambda: messagebox.showinfo("强制删除成功", f"正在处理的任务 {task_id} 已强制删除"))
                            else:
                                self.root.after(0, lambda: self.log(f"✅ 任务删除成功: ID={task_id}"))
                                self.root.after(0, lambda: messagebox.showinfo("删除成功", f"任务 {task_id} 已删除"))

                            self.root.after(0, self.refresh_task_display)
                        else:
                            self.root.after(0, lambda: self.log(f"⚠️ 任务删除失败: 任务不存在"))
                            self.root.after(0, lambda: messagebox.showwarning("删除失败", "任务不存在或已被删除"))

                except Exception as e:
                    error_msg = f"删除任务失败: {str(e)}"
                    self.root.after(0, lambda: self.log(f"❌ {error_msg}"))
                    self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                finally:
                    self._deleting_task = False

            # 在后台线程中执行删除操作
            import threading
            threading.Thread(target=_delete_in_background, daemon=True).start()

        except Exception as e:
            self._deleting_task = False
            error_msg = f"删除任务异常: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def delete_task_from_dialog(self, task_id, task_email, dialog, force_delete=False):
        """从详情对话框删除任务"""
        try:
            # 先关闭对话框
            dialog.destroy()
            # 然后执行删除
            self.delete_task_by_id(task_id, task_email, force_delete=force_delete)
        except Exception as e:
            self.log(f"❌ 从对话框删除任务失败: {e}")

    def retry_failed_task(self, task_id):
        """重试失败的任务"""
        try:
            # 确认对话框
            result = messagebox.askyesno(
                "确认重试",
                f"确定要重新处理任务 {task_id} 吗？\n\n"
                f"任务将被重新加入等待队列。",
                icon='question'
            )

            if not result:
                return

            self.log(f"🔄 开始重试任务: ID={task_id}")

            def _retry_in_background():
                try:
                    beijing_time = self.get_beijing_time()

                    with sqlite3.connect('tasks.db', timeout=10.0) as conn:
                        # 检查任务是否存在且为失败状态
                        cursor = conn.execute('SELECT status FROM task_queue WHERE id = ?', (task_id,))
                        task = cursor.fetchone()

                        if not task:
                            self.root.after(0, lambda: messagebox.showinfo("提示", "任务不存在"))
                            return

                        if task[0] != 'failed':
                            self.root.after(0, lambda: messagebox.showwarning("警告", "只能重试失败的任务"))
                            return

                        # 获取新的队列位置
                        cursor = conn.execute('''
                            SELECT COALESCE(MAX(queue_position), 0) + 1
                            FROM task_queue
                            WHERE status = 'waiting'
                        ''')
                        new_position = cursor.fetchone()[0]

                        # 重置任务状态
                        conn.execute('''
                            UPDATE task_queue
                            SET status = 'waiting',
                                queue_position = ?,
                                started_at = NULL,
                                completed_at = NULL,
                                result_message = NULL,
                                processing_details = NULL
                            WHERE id = ?
                        ''', (new_position, task_id))

                        self.root.after(0, lambda: self.log(f"✅ 任务重试设置成功: ID={task_id}"))
                        self.root.after(0, self.refresh_task_display)
                        self.root.after(0, lambda: messagebox.showinfo("重试成功", f"任务 {task_id} 已重新加入队列"))

                except Exception as e:
                    error_msg = f"重试任务失败: {str(e)}"
                    self.root.after(0, lambda: self.log(f"❌ {error_msg}"))
                    self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

            # 在后台线程中执行重试操作
            import threading
            threading.Thread(target=_retry_in_background, daemon=True).start()

        except Exception as e:
            error_msg = f"重试任务异常: {str(e)}"
            self.log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def on_closing(self):
        """关闭程序（防止重复调用）"""
        # 防止重复关闭
        if self.is_closing:
            return

        self.is_closing = True

        try:
            self.log("🔄 正在关闭程序...")

            # 停止所有后台线程
            self.is_processing = False
            self.timeout_check_enabled = False
            if hasattr(self, 'task_check_enabled'):
                self.task_check_enabled = False
            if hasattr(self, 'status_update_enabled'):
                self.status_update_enabled = False

            # 关闭所有浏览器
            try:
                if hasattr(self, 'browser_manager') and self.browser_manager:
                    # 设置超时，避免浏览器关闭卡死
                    import threading

                    def close_browsers_with_timeout():
                        try:
                            closed_count = self.browser_manager.close_all_browsers()
                            self.log(f"✅ 已关闭 {closed_count} 个浏览器")
                        except Exception as e:
                            self.log(f"⚠️ 关闭浏览器异常: {e}")

                    # 启动关闭线程
                    close_thread = threading.Thread(target=close_browsers_with_timeout, daemon=True)
                    close_thread.start()

                    # 等待最多10秒
                    close_thread.join(timeout=10)

                    if close_thread.is_alive():
                        self.log("⚠️ 浏览器关闭超时，继续程序关闭流程")
                    else:
                        self.log("✅ 浏览器关闭完成")
            except Exception as e:
                self.log(f"⚠️ 关闭浏览器时出错: {e}")

            # 清理资源
            try:
                if hasattr(self, 'cleanup_resources'):
                    self.cleanup_resources()
            except Exception as e:
                self.log(f"⚠️ 清理资源时出错: {e}")

            # 安全关闭GUI
            try:
                if hasattr(self, 'root') and self.root:
                    # 检查窗口是否还存在
                    try:
                        self.root.winfo_exists()
                        self.root.quit()
                        self.root.destroy()
                        self.log("✅ GUI已关闭")
                    except tk.TclError:
                        # 窗口已经被销毁
                        self.log("ℹ️ GUI已经被销毁")
            except Exception as e:
                self.log(f"⚠️ 关闭GUI时出错: {e}")

        except Exception as e:
            print(f"关闭程序时出错: {e}")
            # 不再尝试销毁GUI，直接退出
            import sys
            sys.exit(0)

        finally:
            self.cleanup_completed = True





    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            if not self.is_closing:
                self.log(f"🔄 接收到信号 {signum}，正在优雅关闭...")
                self.on_closing()
            else:
                self.log(f"ℹ️ 信号 {signum} 被忽略，程序正在关闭中...")
                # 强制退出
                import sys
                sys.exit(0)

        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

        # 注册退出处理器
        atexit.register(self.cleanup_on_exit)

    def cleanup_on_exit(self):
        """程序退出时的清理工作"""
        try:
            self.log("🧹 执行退出清理...")
            if hasattr(self, 'browser_manager'):
                self.browser_manager.close_all_browsers()
        except:
            pass

    def cleanup_resources(self):
        """清理系统资源，包括连接池"""
        try:
            self.log("🧹 清理系统资源...")

            # 清理SQLite连接池
            cleanup_all_pools()

            # 清理浏览器资源
            if hasattr(self, 'browser_manager'):
                self.browser_manager.close_all_browsers()

            # 停止后台线程
            if hasattr(self, 'task_check_enabled'):
                self.task_check_enabled = False
            if hasattr(self, 'status_update_enabled'):
                self.status_update_enabled = False
            if hasattr(self, 'timeout_check_enabled'):
                self.timeout_check_enabled = False

            self.log("✅ 资源清理完成")

        except Exception as e:
            self.log(f"⚠️ 资源清理异常: {e}")

    def run(self):
        """运行应用"""
        self.log("🎯 主UI自动处理中心已启动")
        host = self.config['api']['host']
        port = self.config['api']['port']
        # 显示实际的服务器地址
        if host == '0.0.0.0':
            self.log(f"🌐 API服务器地址: http://localhost:{port} (支持外部访问)")
        else:
            self.log(f"🌐 API服务器地址: http://{host}:{port}")
        self.log("💡 现在可以接收外部请求并自动处理")

        # 设置信号处理器
        self.setup_signal_handlers()



        # 注册清理函数
        import atexit
        atexit.register(self.cleanup_resources)

        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 等待一下让Flask完全启动
        self.root.after(3000, lambda: self.log("✅ 系统完全启动，可以接收请求"))

        # 额外等待，确保API完全就绪后再提示
        self.root.after(8000, lambda: self.log("🌐 API服务已就绪，Cloudflare Tunnel可以正常工作"))

        self.root.mainloop()

    # ==================== 用户认证相关API处理方法 ====================

    def handle_register(self):
        """处理用户注册"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            data = request.get_json()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()

            if not username or not password:
                return jsonify({'success': False, 'message': '用户名和密码不能为空'}), 400

            result = self.auth_manager.register_user(username, password)

            if result['success']:
                self.log(f"👤 新用户注册: {username}")
                return jsonify(result), 200
            else:
                return jsonify(result), 400

        except Exception as e:
            self.log(f"❌ 用户注册失败: {e}")
            return jsonify({'success': False, 'message': '注册失败，请稍后重试'}), 500

    def handle_login(self):
        """处理用户登录 - 优化版本，减少阻塞"""
        try:
            # 添加请求日志，帮助调试tunnel问题
            client_ip = request.headers.get('CF-Connecting-IP') or request.remote_addr
            self.log(f"🔐 收到登录请求，来源IP: {client_ip}")

            if not request.is_json:
                self.log(f"⚠️ 登录请求格式错误: Content-Type={request.content_type}")
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            data = request.get_json()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()

            if not username or not password:
                return jsonify({'success': False, 'message': '用户名和密码不能为空'}), 400

            # 添加处理时间记录和超时保护
            start_time = time.time()

            # 使用线程池执行登录，避免阻塞主线程
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                try:
                    # 设置30秒超时
                    future = executor.submit(self.auth_manager.login_user, username, password)
                    result = future.result(timeout=30)
                except concurrent.futures.TimeoutError:
                    self.log(f"⏰ 用户登录超时: {username}")
                    return jsonify({
                        'success': False,
                        'message': '登录请求超时，请稍后重试',
                        'error_type': 'timeout'
                    }), 408

            process_time = time.time() - start_time

            if result['success']:
                self.log(f"👤 用户登录成功: {username} (处理时间: {process_time:.2f}s)")
                # 添加额外的响应信息
                result['server_time'] = time.time()
                result['process_time'] = process_time
                return jsonify(result), 200
            else:
                self.log(f"⚠️ 用户登录失败: {username} - {result.get('message', '未知错误')}")
                return jsonify(result), 401

        except Exception as e:
            self.log(f"❌ 用户登录异常: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")
            return jsonify({
                'success': False,
                'message': '登录失败，请稍后重试',
                'error_type': 'server_error'
            }), 500

    def handle_get_profile(self):
        """处理获取用户信息"""
        try:
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)

            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            return jsonify({
                'success': True,
                'user': user_info
            }), 200

        except Exception as e:
            self.log(f"❌ 获取用户信息失败: {e}")
            return jsonify({'success': False, 'message': '获取用户信息失败'}), 500

    def handle_activate_code(self):
        """处理激活码充值"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            # 验证用户认证
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)

            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            data = request.get_json()
            activation_code = data.get('activation_code', '').strip()

            if not activation_code:
                return jsonify({'success': False, 'message': '激活码不能为空'}), 400

            result = self.auth_manager.recharge_with_activation_code(user_info['id'], activation_code)

            if result['success']:
                self.log(f"💰 用户 {user_info['username']} 充值成功: {activation_code}")
                return jsonify(result), 200
            else:
                return jsonify(result), 400

        except Exception as e:
            self.log(f"❌ 激活码充值失败: {e}")
            return jsonify({'success': False, 'message': '充值失败，请稍后重试'}), 500

    def handle_get_recharge_url(self):
        """处理获取充值链接"""
        try:
            # 使用 AuthManager 的方法获取激活码购买链接
            url = self.auth_manager.get_activation_code_url()
            return jsonify({
                'success': True,
                'url': url,
                'recharge_url': url  # 保持兼容性
            }), 200

        except Exception as e:
            self.log(f"❌ 获取充值链接失败: {e}")
            return jsonify({'success': False, 'message': '获取充值链接失败'}), 500

    def save_user_submission(self, user_id, email, task_id, queue_position, ip_address):
        """保存用户提交记录到历史表"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor()

            beijing_time = self.get_beijing_time()
            insert_query = """
                INSERT INTO user_submissions (user_id, email, task_id, queue_position, ip_address, submitted_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (user_id, email, task_id, queue_position, ip_address, beijing_time))
            connection.commit()

            self.log(f"📝 用户 {user_id} 提交记录已保存: {email}")

        except Exception as e:
            self.log(f"❌ 保存用户提交记录失败: {e}")
        finally:
            if 'connection' in locals() and connection.is_connected():
                cursor.close()
                connection.close()

    def update_user_submission_status(self, task_id, status, result_message=None):
        """更新用户提交记录状态"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor()

            beijing_time = self.get_beijing_time()

            if status == 'processing':
                update_query = """
                    UPDATE user_submissions
                    SET status = %s, started_at = %s
                    WHERE task_id = %s
                """
                cursor.execute(update_query, (status, beijing_time, task_id))
            elif status in ['completed', 'failed']:
                update_query = """
                    UPDATE user_submissions
                    SET status = %s, completed_at = %s, result_message = %s
                    WHERE task_id = %s
                """
                cursor.execute(update_query, (status, beijing_time, result_message, task_id))

            connection.commit()
            self.log(f"📝 任务 {task_id} 状态更新为: {status}")

        except Exception as e:
            self.log(f"❌ 更新用户提交状态失败: {e}")
        finally:
            if 'connection' in locals() and connection.is_connected():
                cursor.close()
                connection.close()

    def handle_get_user_submissions(self):
        """处理获取用户提交历史请求"""
        try:
            # 验证用户认证
            auth_header = request.headers.get('Authorization')
            user_info = self.auth_manager.require_auth(auth_header)

            if not user_info:
                return jsonify({'success': False, 'message': '请先登录'}), 401

            # 获取分页参数
            page = int(request.args.get('page', 1))
            limit = int(request.args.get('limit', 20))
            status_filter = request.args.get('status', '')

            offset = (page - 1) * limit

            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor(dictionary=True)

            # 构建查询条件
            where_conditions = ["user_id = %s"]
            params = [user_info['id']]

            if status_filter:
                where_conditions.append("status = %s")
                params.append(status_filter)

            where_clause = " AND ".join(where_conditions)

            # 获取总数
            count_query = f"SELECT COUNT(*) as total FROM user_submissions WHERE {where_clause}"
            cursor.execute(count_query, params)
            total = cursor.fetchone()['total']

            # 获取分页数据
            query = f"""
                SELECT id, email, status, queue_position, result_message,
                       submitted_at, started_at, completed_at
                FROM user_submissions
                WHERE {where_clause}
                ORDER BY submitted_at DESC
                LIMIT %s OFFSET %s
            """
            cursor.execute(query, params + [limit, offset])
            submissions = cursor.fetchall()

            # 格式化时间
            for submission in submissions:
                if submission['submitted_at']:
                    submission['submitted_at'] = submission['submitted_at'].strftime('%Y-%m-%d %H:%M:%S')
                if submission['started_at']:
                    submission['started_at'] = submission['started_at'].strftime('%Y-%m-%d %H:%M:%S')
                if submission['completed_at']:
                    submission['completed_at'] = submission['completed_at'].strftime('%Y-%m-%d %H:%M:%S')

            return jsonify({
                'success': True,
                'data': {
                    'submissions': submissions,
                    'pagination': {
                        'page': page,
                        'limit': limit,
                        'total': total,
                        'pages': (total + limit - 1) // limit
                    }
                }
            }), 200

        except Exception as e:
            self.log(f"❌ 获取用户提交历史失败: {e}")
            return jsonify({'success': False, 'message': '获取提交历史失败'}), 500
        finally:
            if 'connection' in locals() and connection.is_connected():
                cursor.close()
                connection.close()

    def handle_task_notification(self):
        """处理生产服务器的任务通知"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求格式错误'}), 400

            data = request.get_json()
            action = data.get('action')

            if action == 'new_task':
                self.log("📨 收到生产服务器的新任务通知")
                # 立即触发任务检查
                self.trigger_task_check()
                return jsonify({'success': True, 'message': '任务通知已处理'}), 200
            else:
                return jsonify({'success': False, 'message': '未知的操作'}), 400

        except Exception as e:
            self.log(f"❌ 处理任务通知失败: {e}")
            return jsonify({'success': False, 'message': '处理任务通知失败'}), 500



def main():
    """主函数"""
    app = None
    try:
        app = MainUIWithAPI()
        app.run()
    except KeyboardInterrupt:
        print("\n🔄 检测到用户中断信号 (Ctrl+C)")
        if app and not app.is_closing:
            try:
                app.on_closing()
            except:
                pass
        print("✅ 程序正常退出")
    except Exception as e:
        error_msg = str(e)
        print(f"❌ 程序运行异常: {error_msg}")

        # 避免在Tkinter销毁错误时显示详细堆栈
        if "can't invoke" not in error_msg and "application has been destroyed" not in error_msg:
            import traceback
            traceback.print_exc()
        else:
            print("ℹ️ 这是Tkinter窗口重复销毁错误，程序正在正常关闭")

        # 尝试优雅关闭
        if app and not getattr(app, 'is_closing', False):
            try:
                app.on_closing()
            except:
                pass
    finally:
        print("🔚 程序已结束")

if __name__ == "__main__":
    main()

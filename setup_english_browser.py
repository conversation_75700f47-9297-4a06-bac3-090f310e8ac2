#!/usr/bin/env python3
"""
设置英语浏览器环境的脚本
1. 卸载timezone插件，安装ncb.crx插件
2. 将浏览器语言设置为英语
3. 检查并修复可能暴露中国地区的设置
"""
import json
import os
import shutil
import sys
import zipfile
import tempfile

def extract_crx_to_folder(crx_path, output_folder):
    """将CRX文件解压到指定文件夹"""
    try:
        with open(crx_path, 'rb') as f:
            # 读取CRX头部
            magic = f.read(4)
            if magic != b'Cr24':
                print(f"❌ 不是有效的CRX文件: {crx_path}")
                return False

            version = int.from_bytes(f.read(4), 'little')
            print(f"📋 CRX版本: {version}")

            if version == 2:
                # CRX v2格式
                pub_key_len = int.from_bytes(f.read(4), 'little')
                sig_len = int.from_bytes(f.read(4), 'little')
                print(f"📋 公钥长度: {pub_key_len}, 签名长度: {sig_len}")

                # 跳过公钥和签名
                f.seek(16 + pub_key_len + sig_len)

            elif version == 3:
                # CRX v3格式
                header_len = int.from_bytes(f.read(4), 'little')
                print(f"📋 头部长度: {header_len}")

                # 跳过头部
                f.seek(12 + header_len)

            else:
                print(f"❌ 不支持的CRX版本: {version}")
                return False

            # 读取ZIP数据
            zip_data = f.read()

            if not zip_data:
                print("❌ 没有找到ZIP数据")
                return False

            print(f"📦 ZIP数据大小: {len(zip_data)} 字节")

        # 创建临时ZIP文件
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip:
            temp_zip.write(zip_data)
            temp_zip_path = temp_zip.name

        try:
            # 解压ZIP文件
            with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
                zip_ref.extractall(output_folder)
            print(f"✅ 成功解压CRX文件到: {output_folder}")
            return True
        except zipfile.BadZipFile as e:
            print(f"❌ ZIP文件损坏: {e}")
            # 尝试查找ZIP文件头
            with open(crx_path, 'rb') as f:
                content = f.read()
                pk_pos = content.find(b'PK\x03\x04')
                if pk_pos > 0:
                    print(f"💡 在位置 {pk_pos} 找到ZIP签名，尝试从该位置解压...")
                    zip_data = content[pk_pos:]
                    with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip2:
                        temp_zip2.write(zip_data)
                        temp_zip_path2 = temp_zip2.name

                    try:
                        with zipfile.ZipFile(temp_zip_path2, 'r') as zip_ref:
                            zip_ref.extractall(output_folder)
                        print(f"✅ 成功解压CRX文件到: {output_folder}")
                        return True
                    finally:
                        os.unlink(temp_zip_path2)
            return False
        finally:
            # 清理临时文件
            if os.path.exists(temp_zip_path):
                os.unlink(temp_zip_path)

    except Exception as e:
        print(f"❌ 解压CRX文件失败: {e}")
        return False

def remove_timezone_plugin():
    """移除timezone插件"""
    print("🗑️ 开始移除timezone插件...")
    
    chrome_base = "138/chrome-win64"
    chrome_data_paths = [
        f"{chrome_base}/chrome-debug-data/Default",
        f"{chrome_base}/chrome-debug-profile/Default"
    ]
    
    timezone_extension_id = "timezone12345678901234567890123"
    
    for chrome_data_path in chrome_data_paths:
        if not os.path.exists(chrome_data_path):
            continue
            
        # 删除扩展文件夹
        extensions_dir = f"{chrome_data_path}/Extensions"
        timezone_ext_dir = f"{extensions_dir}/{timezone_extension_id}"
        
        if os.path.exists(timezone_ext_dir):
            try:
                shutil.rmtree(timezone_ext_dir)
                print(f"✅ 删除timezone扩展目录: {timezone_ext_dir}")
            except Exception as e:
                print(f"❌ 删除扩展目录失败: {e}")
        
        # 修改Preferences文件移除扩展配置
        preferences_file = f"{chrome_data_path}/Preferences"
        if os.path.exists(preferences_file):
            try:
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    prefs = json.load(f)
                
                # 移除timezone扩展配置
                if 'extensions' in prefs and 'settings' in prefs['extensions']:
                    if timezone_extension_id in prefs['extensions']['settings']:
                        del prefs['extensions']['settings'][timezone_extension_id]
                        print(f"✅ 从Preferences中移除timezone插件配置")
                
                # 写回配置文件
                with open(preferences_file, 'w', encoding='utf-8') as f:
                    json.dump(prefs, f, separators=(',', ':'))
                    
            except Exception as e:
                print(f"❌ 修改Preferences文件失败: {e}")

def install_ncb_extension():
    """安装ncb.crx插件"""
    print("📦 开始安装ncb.crx插件...")
    
    crx_path = "ncb.crx"
    if not os.path.exists(crx_path):
        print(f"❌ 未找到ncb.crx文件: {crx_path}")
        return False
    
    # 创建ncb插件目录
    ncb_folder = "ncb"
    if os.path.exists(ncb_folder):
        shutil.rmtree(ncb_folder)
    
    os.makedirs(ncb_folder, exist_ok=True)
    
    # 解压CRX文件
    if not extract_crx_to_folder(crx_path, ncb_folder):
        return False
    
    # 检查manifest.json
    manifest_path = os.path.join(ncb_folder, "manifest.json")
    if not os.path.exists(manifest_path):
        print(f"❌ 解压后未找到manifest.json文件")
        return False
    
    # 读取manifest信息
    try:
        with open(manifest_path, 'r', encoding='utf-8') as f:
            manifest = json.load(f)
        
        extension_name = manifest.get('name', 'NCB Extension')
        extension_version = manifest.get('version', '1.0.0')
        print(f"📋 插件信息: {extension_name} v{extension_version}")
        
    except Exception as e:
        print(f"⚠️ 读取manifest.json失败: {e}")
        extension_name = "NCB Extension"
        extension_version = "1.0.0"
    
    # 安装到Chrome配置中
    chrome_base = "138/chrome-win64"
    chrome_data_paths = [
        f"{chrome_base}/chrome-debug-data/Default",
        f"{chrome_base}/chrome-debug-profile/Default"
    ]
    
    ncb_extension_id = "ncbextension123456789012345678901"  # NCB插件的自定义扩展ID
    extension_version_folder = f"{extension_version}_0"
    
    for chrome_data_path in chrome_data_paths:
        if not os.path.exists(chrome_data_path):
            continue
            
        print(f"📁 处理Chrome数据目录: {chrome_data_path}")
        
        # 创建扩展目录
        extensions_dir = f"{chrome_data_path}/Extensions"
        extension_dir = f"{extensions_dir}/{ncb_extension_id}/{extension_version_folder}"
        
        os.makedirs(extension_dir, exist_ok=True)
        
        # 复制插件文件
        try:
            for root, dirs, files in os.walk(ncb_folder):
                for file in files:
                    src_file = os.path.join(root, file)
                    rel_path = os.path.relpath(src_file, ncb_folder)
                    dst_file = os.path.join(extension_dir, rel_path)
                    
                    os.makedirs(os.path.dirname(dst_file), exist_ok=True)
                    shutil.copy2(src_file, dst_file)
            
            print(f"✅ 复制ncb插件文件到: {extension_dir}")
        except Exception as e:
            print(f"❌ 复制文件失败: {e}")
            continue
        
        # 修改Preferences文件以启用扩展
        preferences_file = f"{chrome_data_path}/Preferences"
        if os.path.exists(preferences_file):
            try:
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    prefs = json.load(f)
                
                # 确保extensions节点存在
                if 'extensions' not in prefs:
                    prefs['extensions'] = {}
                if 'settings' not in prefs['extensions']:
                    prefs['extensions']['settings'] = {}
                
                # 添加ncb扩展配置
                prefs['extensions']['settings'][ncb_extension_id] = {
                    "active_permissions": {
                        "api": manifest.get('permissions', []),
                        "explicit_host": manifest.get('host_permissions', []),
                        "manifest_permissions": []
                    },
                    "creation_flags": 1,
                    "from_webstore": False,
                    "install_time": "13395901800000000",
                    "location": 4,  # EXTERNAL_PREF
                    "manifest": manifest,
                    "path": extension_dir,
                    "state": 1,  # ENABLED
                    "was_installed_by_default": False,
                    "was_installed_by_oem": False
                }
                
                # 写回配置文件
                with open(preferences_file, 'w', encoding='utf-8') as f:
                    json.dump(prefs, f, separators=(',', ':'))
                
                print(f"✅ 更新Preferences文件: {preferences_file}")
                
            except Exception as e:
                print(f"❌ 修改Preferences文件失败: {e}")
    
    print("🎉 ncb插件安装完成！")
    return True

def setup_english_language():
    """设置浏览器语言为英语"""
    print("🌍 开始设置浏览器语言为英语...")
    
    chrome_base = "138/chrome-win64"
    chrome_data_paths = [
        f"{chrome_base}/chrome-debug-data/Default",
        f"{chrome_base}/chrome-debug-profile/Default"
    ]
    
    for chrome_data_path in chrome_data_paths:
        if not os.path.exists(chrome_data_path):
            continue
            
        preferences_file = f"{chrome_data_path}/Preferences"
        if os.path.exists(preferences_file):
            try:
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    prefs = json.load(f)
                
                # 设置语言相关配置
                if 'intl' not in prefs:
                    prefs['intl'] = {}
                
                # 设置接受的语言
                prefs['intl']['accept_languages'] = "en-US,en"
                prefs['intl']['selected_languages'] = ["en-US", "en"]
                
                # 设置地区相关
                if 'browser' not in prefs:
                    prefs['browser'] = {}
                
                # 移除可能暴露中国地区的设置
                prefs['browser']['default_search_provider_data'] = {
                    "template_url_data": {
                        "url": "https://www.google.com/search?q={searchTerms}",
                        "short_name": "Google",
                        "keyword": "google.com"
                    }
                }
                
                # 设置时区为通用时区（避免暴露地理位置）
                if 'profile' not in prefs:
                    prefs['profile'] = {}
                
                # 写回配置文件
                with open(preferences_file, 'w', encoding='utf-8') as f:
                    json.dump(prefs, f, separators=(',', ':'))
                
                print(f"✅ 设置英语语言配置: {preferences_file}")
                
            except Exception as e:
                print(f"❌ 设置语言配置失败: {e}")

def main():
    """主函数"""
    print("🔧 开始设置英语浏览器环境")
    print("=" * 50)
    
    # 1. 移除timezone插件
    remove_timezone_plugin()
    print()
    
    # 2. 安装ncb插件
    if install_ncb_extension():
        print()
    else:
        print("❌ ncb插件安装失败，继续其他设置...")
        print()
    
    # 3. 设置英语语言
    setup_english_language()
    print()
    
    print("🎉 英语浏览器环境设置完成！")
    print("📝 已完成的设置:")
    print("   ✅ 移除timezone插件")
    print("   ✅ 安装ncb.crx插件")
    print("   ✅ 设置浏览器语言为英语")
    print("   ✅ 移除可能暴露中国地区的配置")

if __name__ == "__main__":
    main()

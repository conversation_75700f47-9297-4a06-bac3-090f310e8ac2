#!/usr/bin/env python3
"""
简单的美国时区测试
"""
import os
import sys
import time
from browser_manager import <PERSON>rowserManager

def simple_us_timezone_test():
    """简单的美国时区测试"""
    print("🧪 开始简单的美国时区测试...")
    
    # 初始化浏览器管理器
    try:
        browser_manager = BrowserManager()
        print("✅ BrowserManager初始化成功")
    except Exception as e:
        print(f"❌ BrowserManager初始化失败: {e}")
        return False
    
    # 创建测试配置文件
    test_profile_name = "simple_us_test"
    
    try:
        # 检查是否已存在测试配置
        existing_profile = browser_manager.config_manager.get_profile(test_profile_name)
        if not existing_profile:
            # 创建新的测试配置
            browser_manager.config_manager.create_profile(test_profile_name)
            print(f"✅ 创建测试配置: {test_profile_name}")
        else:
            print(f"✅ 使用现有测试配置: {test_profile_name}")
    
    except Exception as e:
        print(f"❌ 创建测试配置失败: {e}")
        return False
    
    # 测试启动浏览器
    try:
        print("🚀 启动带有美国时区设置的Chrome浏览器...")
        browser_info = browser_manager.launch_browser(test_profile_name)
        
        if browser_info:
            print(f"✅ Chrome启动成功!")
            print(f"   PID: {browser_info['pid']}")
            print(f"   调试端口: {browser_info['debug_port']}")
            
            # 等待几秒钟让浏览器完全启动
            print("⏳ 等待浏览器完全启动...")
            time.sleep(5)
            
            # 测试WebDriver连接
            try:
                print("🔗 测试WebDriver连接...")
                driver = browser_manager.get_automation_driver(test_profile_name)
                
                if driver:
                    print("✅ WebDriver连接成功!")
                    
                    # 访问时区检测页面
                    print("🌍 访问时区检测页面...")
                    driver.get("https://whatismytimezone.com/")
                    time.sleep(5)
                    
                    # 获取页面标题
                    page_title = driver.title
                    print(f"📄 页面标题: {page_title}")
                    
                    # 执行JavaScript获取时区信息
                    timezone_info = driver.execute_script("""
                        return {
                            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                            language: navigator.language,
                            languages: navigator.languages,
                            userAgent: navigator.userAgent,
                            date: new Date().toString(),
                            localeDate: new Date().toLocaleString(),
                            timezoneOffset: new Date().getTimezoneOffset()
                        };
                    """)
                    
                    print("📊 浏览器时区信息:")
                    print(f"   🕐 时区: {timezone_info['timezone']}")
                    print(f"   🌐 语言: {timezone_info['language']}")
                    print(f"   📅 日期: {timezone_info['date']}")
                    print(f"   📍 本地日期: {timezone_info['localeDate']}")
                    print(f"   ⏰ 时区偏移: {timezone_info['timezoneOffset']} 分钟")
                    
                    # 检查是否为美国时区
                    us_timezones = [
                        "America/New_York", "America/Chicago", "America/Denver", 
                        "America/Los_Angeles", "America/Anchorage", "Pacific/Honolulu"
                    ]
                    
                    success = False
                    if timezone_info['timezone'] in us_timezones:
                        print("✅ 时区设置成功 - 已设置为美国时区")
                        success = True
                    else:
                        print(f"⚠️ 时区可能未正确设置: {timezone_info['timezone']}")
                    
                    if timezone_info['language'].startswith('en'):
                        print("✅ 语言设置成功 - 已设置为英语")
                        success = True
                    else:
                        print(f"⚠️ 语言可能未正确设置: {timezone_info['language']}")
                    
                    # 尝试获取页面上的时区信息
                    try:
                        page_timezone = driver.execute_script("""
                            const elements = document.querySelectorAll('*');
                            for (let el of elements) {
                                if (el.textContent && el.textContent.includes('Time Zone')) {
                                    return el.textContent;
                                }
                            }
                            return 'Not found';
                        """)
                        print(f"🌐 页面显示的时区信息: {page_timezone}")
                    except:
                        pass
                    
                    # 保持浏览器运行，让用户手动检查
                    print("\n🔍 浏览器将保持运行，您可以手动检查以下内容:")
                    print("   1. 访问 https://whatismytimezone.com/ 查看时区")
                    print("   2. 访问 https://whatismyipaddress.com/ 查看位置")
                    print("   3. 按F12打开开发者工具，在控制台输入:")
                    print("      console.log(Intl.DateTimeFormat().resolvedOptions().timeZone)")
                    print("      console.log(new Date().toString())")
                    print("      console.log(navigator.language)")
                    
                    # 关闭WebDriver连接但保持浏览器运行
                    driver.quit()
                    print("✅ WebDriver连接已关闭，浏览器继续运行")
                    
                    return success
                    
                else:
                    print("❌ WebDriver连接失败")
                    return False
                    
            except Exception as e:
                print(f"❌ WebDriver测试失败: {e}")
                return False
            
        else:
            print("❌ Chrome启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 浏览器启动测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 简单美国时区测试")
    print("=" * 60)
    
    try:
        success = simple_us_timezone_test()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 美国时区测试完成！")
            print("✅ 浏览器已启动并配置美国设置")
            print("💡 请手动检查浏览器中的时区和位置信息")
        else:
            print("⚠️ 美国时区测试完成，但可能需要手动验证")
            print("💡 请在浏览器中手动检查时区设置")
        print("=" * 60)
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消测试")
        return False
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

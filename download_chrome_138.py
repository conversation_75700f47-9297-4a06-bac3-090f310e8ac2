#!/usr/bin/env python3
"""
自动下载Chrome 138版本并替换当前134版本的脚本
"""
import os
import sys
import shutil
import zipfile
import requests
from pathlib import Path
import tempfile
import json

class Chrome138Downloader:
    """Chrome 138版本下载器"""
    
    def __init__(self):
        self.base_url = "https://storage.googleapis.com/chrome-for-testing-public"
        self.version = "138.0.7204.168"  # Chrome 138最新稳定版本
        self.platform = "win64"
        self.current_dir = Path.cwd()
        self.target_dir = self.current_dir / "138"
        self.old_dir = self.current_dir / "134"
        
    def get_download_urls(self):
        """获取Chrome 138下载链接"""
        urls = {
            'chrome': f"{self.base_url}/{self.version}/{self.platform}/chrome-{self.platform}.zip",
            'chromedriver': f"{self.base_url}/{self.version}/{self.platform}/chromedriver-{self.platform}.zip",
            'chrome_headless': f"{self.base_url}/{self.version}/{self.platform}/chrome-headless-shell-{self.platform}.zip"
        }
        return urls
    
    def download_file(self, url, filename):
        """下载文件"""
        print(f"📥 正在下载: {filename}")
        print(f"🔗 下载链接: {url}")
        
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            print(f"\r📊 下载进度: {progress:.1f}%", end='', flush=True)
            
            print(f"\n✅ 下载完成: {filename}")
            return True
            
        except Exception as e:
            print(f"\n❌ 下载失败: {e}")
            return False
    
    def extract_zip(self, zip_path, extract_to):
        """解压ZIP文件"""
        print(f"📦 正在解压: {zip_path}")
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
            print(f"✅ 解压完成: {extract_to}")
            return True
        except Exception as e:
            print(f"❌ 解压失败: {e}")
            return False
    
    def backup_old_version(self):
        """备份旧版本"""
        if self.old_dir.exists():
            backup_dir = self.current_dir / f"134_backup_{int(time.time())}"
            print(f"📋 备份旧版本到: {backup_dir}")
            try:
                shutil.move(str(self.old_dir), str(backup_dir))
                print("✅ 备份完成")
                return True
            except Exception as e:
                print(f"❌ 备份失败: {e}")
                return False
        return True
    
    def update_browser_manager(self):
        """更新browser_manager.py中的版本引用"""
        browser_manager_path = self.current_dir / "browser_manager.py"
        
        if not browser_manager_path.exists():
            print("⚠️ 未找到browser_manager.py文件")
            return False
        
        print("🔧 更新browser_manager.py中的版本引用...")
        
        try:
            # 读取文件内容
            with open(browser_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换版本引用
            replacements = [
                ('134', '138'),
                ('134.0.6998.35', '138.0.7204.168'),
                ('"134"', '"138"'),
                ("'134'", "'138'"),
                ('134/', '138/'),
                ('134\\', '138\\')
            ]
            
            for old, new in replacements:
                content = content.replace(old, new)
            
            # 写回文件
            with open(browser_manager_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ browser_manager.py更新完成")
            return True
            
        except Exception as e:
            print(f"❌ 更新browser_manager.py失败: {e}")
            return False
    
    def update_stealth_browser_manager(self):
        """更新stealth_browser_manager.py中的版本引用"""
        stealth_manager_path = self.current_dir / "stealth_browser_manager.py"

        if not stealth_manager_path.exists():
            print("⚠️ 未找到stealth_browser_manager.py文件")
            return False

        print("🔧 更新stealth_browser_manager.py中的版本引用...")

        try:
            # 读取文件内容
            with open(stealth_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 替换版本引用
            replacements = [
                ('./134/chrome-win64/chrome.exe', './138/chrome-win64/chrome.exe'),
                ('134\\chrome-win64\\chrome.exe', '138\\chrome-win64\\chrome.exe'),
                ('"134"', '"138"'),
                ("'134'", "'138'")
            ]

            for old, new in replacements:
                content = content.replace(old, new)

            # 写回文件
            with open(stealth_manager_path, 'w', encoding='utf-8') as f:
                f.write(content)

            print("✅ stealth_browser_manager.py更新完成")
            return True

        except Exception as e:
            print(f"❌ 更新stealth_browser_manager.py失败: {e}")
            return False

    def update_other_files(self):
        """更新其他相关文件中的版本引用"""
        files_to_update = [
            "native_browser_analyzer.py",
            "check_china_exposure.py",
            "install_finger_extension.py",
            "setup_english_browser.py"
        ]

        print("🔧 更新其他相关文件中的版本引用...")

        for filename in files_to_update:
            file_path = self.current_dir / filename
            if not file_path.exists():
                print(f"⚠️ 跳过不存在的文件: {filename}")
                continue

            try:
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 替换版本引用
                replacements = [
                    ('134', '138'),
                    ('"134"', '"138"'),
                    ("'134'", "'138'"),
                    ('134/', '138/'),
                    ('134\\', '138\\')
                ]

                modified = False
                for old, new in replacements:
                    if old in content:
                        content = content.replace(old, new)
                        modified = True

                if modified:
                    # 写回文件
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"✅ 更新完成: {filename}")
                else:
                    print(f"ℹ️ 无需更新: {filename}")

            except Exception as e:
                print(f"❌ 更新{filename}失败: {e}")

        return True
    
    def download_and_install(self):
        """下载并安装Chrome 138"""
        print("🚀 开始下载Chrome 138版本...")
        print(f"📁 目标目录: {self.target_dir}")
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 获取下载链接
            urls = self.get_download_urls()
            
            # 下载所有文件
            downloads = {}
            for name, url in urls.items():
                filename = temp_path / f"{name}.zip"
                if self.download_file(url, filename):
                    downloads[name] = filename
                else:
                    print(f"❌ 下载{name}失败，终止安装")
                    return False
            
            # 创建目标目录
            if self.target_dir.exists():
                print(f"🗑️ 删除现有目录: {self.target_dir}")
                shutil.rmtree(self.target_dir)
            
            self.target_dir.mkdir(parents=True, exist_ok=True)
            
            # 解压所有文件
            for name, zip_path in downloads.items():
                if name == 'chrome':
                    extract_to = self.target_dir
                elif name == 'chromedriver':
                    extract_to = self.target_dir
                else:  # chrome_headless
                    extract_to = self.target_dir
                
                if not self.extract_zip(zip_path, extract_to):
                    print(f"❌ 解压{name}失败，终止安装")
                    return False
            
            # 移动chromedriver.exe到根目录
            chromedriver_in_folder = self.target_dir / f"chromedriver-{self.platform}" / "chromedriver.exe"
            chromedriver_target = self.target_dir / "chromedriver.exe"
            
            if chromedriver_in_folder.exists():
                shutil.move(str(chromedriver_in_folder), str(chromedriver_target))
                # 删除空文件夹
                shutil.rmtree(self.target_dir / f"chromedriver-{self.platform}")
                print("✅ ChromeDriver移动到根目录")
        
        print("🎉 Chrome 138下载安装完成！")
        return True
    
    def run(self):
        """执行完整的升级流程"""
        print("=" * 60)
        print("🔄 Chrome 138 自动升级工具")
        print("=" * 60)
        
        # 步骤1: 备份旧版本
        if not self.backup_old_version():
            return False
        
        # 步骤2: 下载并安装新版本
        if not self.download_and_install():
            return False
        
        # 步骤3: 更新代码中的版本引用
        self.update_browser_manager()
        self.update_stealth_browser_manager()
        self.update_other_files()

        print("\n" + "=" * 60)
        print("✅ Chrome 138升级完成！")
        print("📋 升级摘要:")
        print(f"   • 新版本: Chrome {self.version}")
        print(f"   • 安装路径: {self.target_dir}")
        print(f"   • 旧版本已备份")
        print("   • 代码文件已更新")
        print("   • 所有相关文件已同步更新")
        print("=" * 60)
        
        return True

if __name__ == "__main__":
    import time
    
    downloader = Chrome138Downloader()
    
    try:
        success = downloader.run()
        if success:
            print("\n🎉 升级成功！您现在可以使用Chrome 138版本了。")
        else:
            print("\n❌ 升级失败！请检查错误信息。")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 发生未预期的错误: {e}")
        sys.exit(1)

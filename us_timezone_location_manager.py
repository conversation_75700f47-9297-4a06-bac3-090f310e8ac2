#!/usr/bin/env python3
"""
美国时区和地理位置管理器
模拟ADS指纹浏览器的时区修改方式
"""
import os
import json
import random
import time
from typing import Dict, List, Tuple

class USTimezoneLocationManager:
    """美国时区和地理位置管理器"""
    
    def __init__(self):
        self.us_timezones = {
            # 美国主要时区配置
            "America/New_York": {
                "name": "Eastern Time",
                "offset": -5,  # UTC-5 (EST) / UTC-4 (EDT)
                "dst": True,
                "cities": ["New York", "Miami", "Boston", "Atlanta", "Philadelphia"],
                "states": ["NY", "FL", "MA", "GA", "PA", "NC", "SC", "VA", "MD", "DE", "NJ", "CT", "RI", "VT", "NH", "ME"],
                "coordinates": [
                    {"lat": 40.7128, "lng": -74.0060, "city": "New York"},
                    {"lat": 25.7617, "lng": -80.1918, "city": "Miami"},
                    {"lat": 42.3601, "lng": -71.0589, "city": "Boston"},
                    {"lat": 33.7490, "lng": -84.3880, "city": "Atlanta"},
                    {"lat": 39.9526, "lng": -75.1652, "city": "Philadelphia"}
                ]
            },
            "America/Chicago": {
                "name": "Central Time",
                "offset": -6,  # UTC-6 (CST) / UTC-5 (CDT)
                "dst": True,
                "cities": ["Chicago", "Houston", "Dallas", "San Antonio", "Austin"],
                "states": ["IL", "TX", "MO", "TN", "AR", "LA", "MS", "AL", "WI", "MN", "IA", "KS", "OK", "NE", "ND", "SD"],
                "coordinates": [
                    {"lat": 41.8781, "lng": -87.6298, "city": "Chicago"},
                    {"lat": 29.7604, "lng": -95.3698, "city": "Houston"},
                    {"lat": 32.7767, "lng": -96.7970, "city": "Dallas"},
                    {"lat": 29.4241, "lng": -98.4936, "city": "San Antonio"},
                    {"lat": 30.2672, "lng": -97.7431, "city": "Austin"}
                ]
            },
            "America/Denver": {
                "name": "Mountain Time",
                "offset": -7,  # UTC-7 (MST) / UTC-6 (MDT)
                "dst": True,
                "cities": ["Denver", "Phoenix", "Salt Lake City", "Albuquerque", "Colorado Springs"],
                "states": ["CO", "AZ", "UT", "NM", "WY", "MT", "ID"],
                "coordinates": [
                    {"lat": 39.7392, "lng": -104.9903, "city": "Denver"},
                    {"lat": 33.4484, "lng": -112.0740, "city": "Phoenix"},
                    {"lat": 40.7608, "lng": -111.8910, "city": "Salt Lake City"},
                    {"lat": 35.0844, "lng": -106.6504, "city": "Albuquerque"},
                    {"lat": 38.8339, "lng": -104.8214, "city": "Colorado Springs"}
                ]
            },
            "America/Los_Angeles": {
                "name": "Pacific Time",
                "offset": -8,  # UTC-8 (PST) / UTC-7 (PDT)
                "dst": True,
                "cities": ["Los Angeles", "San Francisco", "Seattle", "San Diego", "Las Vegas"],
                "states": ["CA", "WA", "OR", "NV"],
                "coordinates": [
                    {"lat": 34.0522, "lng": -118.2437, "city": "Los Angeles"},
                    {"lat": 37.7749, "lng": -122.4194, "city": "San Francisco"},
                    {"lat": 47.6062, "lng": -122.3321, "city": "Seattle"},
                    {"lat": 32.7157, "lng": -117.1611, "city": "San Diego"},
                    {"lat": 36.1699, "lng": -115.1398, "city": "Las Vegas"}
                ]
            },
            "America/Anchorage": {
                "name": "Alaska Time",
                "offset": -9,  # UTC-9 (AKST) / UTC-8 (AKDT)
                "dst": True,
                "cities": ["Anchorage", "Fairbanks", "Juneau"],
                "states": ["AK"],
                "coordinates": [
                    {"lat": 61.2181, "lng": -149.9003, "city": "Anchorage"},
                    {"lat": 64.8378, "lng": -147.7164, "city": "Fairbanks"},
                    {"lat": 58.3019, "lng": -134.4197, "city": "Juneau"}
                ]
            },
            "Pacific/Honolulu": {
                "name": "Hawaii Time",
                "offset": -10,  # UTC-10 (HST, no DST)
                "dst": False,
                "cities": ["Honolulu", "Hilo", "Kailua"],
                "states": ["HI"],
                "coordinates": [
                    {"lat": 21.3099, "lng": -157.8581, "city": "Honolulu"},
                    {"lat": 19.7297, "lng": -155.0900, "city": "Hilo"},
                    {"lat": 21.4022, "lng": -157.7394, "city": "Kailua"}
                ]
            }
        }
        
        # 美国常用的语言设置
        self.us_languages = [
            "en-US,en;q=0.9",
            "en-US,en;q=0.8,es;q=0.6",
            "en-US,en;q=0.9,es;q=0.8",
            "en-US"
        ]
        
        # 美国常用的User-Agent模板
        self.us_user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.168 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.168 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        ]
    
    def get_random_us_location(self, timezone_name: str = None) -> Dict:
        """获取随机的美国地理位置"""
        if timezone_name and timezone_name in self.us_timezones:
            timezone_data = self.us_timezones[timezone_name]
        else:
            # 随机选择一个时区
            timezone_name = random.choice(list(self.us_timezones.keys()))
            timezone_data = self.us_timezones[timezone_name]
        
        # 随机选择该时区的一个城市
        location = random.choice(timezone_data["coordinates"])
        
        # 添加一些随机偏移以模拟真实位置
        lat_offset = random.uniform(-0.1, 0.1)
        lng_offset = random.uniform(-0.1, 0.1)
        
        return {
            "timezone": timezone_name,
            "timezone_name": timezone_data["name"],
            "latitude": round(location["lat"] + lat_offset, 6),
            "longitude": round(location["lng"] + lng_offset, 6),
            "city": location["city"],
            "state": random.choice(timezone_data["states"]),
            "country": "United States",
            "country_code": "US",
            "accuracy": random.randint(10, 100),
            "offset": timezone_data["offset"],
            "dst": timezone_data["dst"]
        }
    
    def create_timezone_extension(self, location_data: Dict) -> str:
        """创建时区修改扩展"""
        extension_dir = "us_timezone_extension"
        
        # 创建扩展目录
        os.makedirs(extension_dir, exist_ok=True)
        
        # 创建manifest.json
        manifest = {
            "manifest_version": 3,
            "name": "US Timezone & Location Spoofer",
            "version": "1.0.0",
            "description": "Spoof timezone and geolocation to US locations",
            "permissions": [
                "storage",
                "scripting"
            ],
            "host_permissions": [
                "*://*/*"
            ],
            "content_scripts": [
                {
                    "matches": ["*://*/*"],
                    "js": ["content.js"],
                    "run_at": "document_start",
                    "all_frames": True
                }
            ],
            "background": {
                "service_worker": "background.js"
            }
        }
        
        with open(os.path.join(extension_dir, "manifest.json"), 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2)
        
        # 创建content.js - 时区和地理位置欺骗脚本
        content_js = f'''
// US Timezone & Location Spoofer
(function() {{
    'use strict';
    
    const LOCATION_DATA = {json.dumps(location_data)};
    
    // 1. 时区欺骗
    const originalDateTimeFormat = Intl.DateTimeFormat;
    const originalResolvedOptions = originalDateTimeFormat.prototype.resolvedOptions;
    
    Intl.DateTimeFormat.prototype.resolvedOptions = function() {{
        const options = originalResolvedOptions.call(this);
        options.timeZone = LOCATION_DATA.timezone;
        return options;
    }};
    
    // 重写Date对象的时区相关方法
    const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
    Date.prototype.getTimezoneOffset = function() {{
        return LOCATION_DATA.offset * -60; // 转换为分钟
    }};
    
    // 2. 地理位置欺骗
    if (navigator.geolocation) {{
        const originalGetCurrentPosition = navigator.geolocation.getCurrentPosition;
        const originalWatchPosition = navigator.geolocation.watchPosition;
        
        navigator.geolocation.getCurrentPosition = function(success, error, options) {{
            if (success) {{
                const position = {{
                    coords: {{
                        latitude: LOCATION_DATA.latitude,
                        longitude: LOCATION_DATA.longitude,
                        accuracy: LOCATION_DATA.accuracy,
                        altitude: null,
                        altitudeAccuracy: null,
                        heading: null,
                        speed: null
                    }},
                    timestamp: Date.now()
                }};
                success(position);
            }}
        }};
        
        navigator.geolocation.watchPosition = function(success, error, options) {{
            if (success) {{
                const position = {{
                    coords: {{
                        latitude: LOCATION_DATA.latitude,
                        longitude: LOCATION_DATA.longitude,
                        accuracy: LOCATION_DATA.accuracy,
                        altitude: null,
                        altitudeAccuracy: null,
                        heading: null,
                        speed: null
                    }},
                    timestamp: Date.now()
                }};
                success(position);
            }}
            return 1; // 返回watchId
        }};
    }}
    
    // 3. 语言设置
    Object.defineProperty(navigator, 'language', {{
        get: function() {{ return 'en-US'; }}
    }});
    
    Object.defineProperty(navigator, 'languages', {{
        get: function() {{ return ['en-US', 'en']; }}
    }});
    
    // 4. 时间格式
    const originalToLocaleString = Date.prototype.toLocaleString;
    const originalToLocaleDateString = Date.prototype.toLocaleDateString;
    const originalToLocaleTimeString = Date.prototype.toLocaleTimeString;
    
    Date.prototype.toLocaleString = function(locales, options) {{
        return originalToLocaleString.call(this, 'en-US', {{
            ...options,
            timeZone: LOCATION_DATA.timezone
        }});
    }};
    
    Date.prototype.toLocaleDateString = function(locales, options) {{
        return originalToLocaleDateString.call(this, 'en-US', {{
            ...options,
            timeZone: LOCATION_DATA.timezone
        }});
    }};
    
    Date.prototype.toLocaleTimeString = function(locales, options) {{
        return originalToLocaleTimeString.call(this, 'en-US', {{
            ...options,
            timeZone: LOCATION_DATA.timezone
        }});
    }};
    
    // 5. 控制台输出调试信息
    console.log('🇺🇸 US Timezone & Location Spoofer Active');
    console.log('📍 Location:', LOCATION_DATA.city + ', ' + LOCATION_DATA.state);
    console.log('🕐 Timezone:', LOCATION_DATA.timezone);
    console.log('📊 Coordinates:', LOCATION_DATA.latitude + ', ' + LOCATION_DATA.longitude);
    
}})();
'''
        
        with open(os.path.join(extension_dir, "content.js"), 'w', encoding='utf-8') as f:
            f.write(content_js)
        
        # 创建background.js
        background_js = '''
// Background script for US Timezone & Location Spoofer
chrome.runtime.onInstalled.addListener(() => {
    console.log('US Timezone & Location Spoofer installed');
});
'''
        
        with open(os.path.join(extension_dir, "background.js"), 'w', encoding='utf-8') as f:
            f.write(background_js)
        
        print(f"✅ 美国时区扩展已创建: {extension_dir}")
        print(f"📍 位置: {location_data['city']}, {location_data['state']}")
        print(f"🕐 时区: {location_data['timezone']} ({location_data['timezone_name']})")
        print(f"📊 坐标: {location_data['latitude']}, {location_data['longitude']}")
        
        return extension_dir
    
    def update_browser_manager_for_us(self):
        """更新browser_manager.py以支持美国时区扩展"""
        browser_manager_path = "browser_manager.py"
        
        if not os.path.exists(browser_manager_path):
            print("❌ 未找到browser_manager.py文件")
            return False
        
        print("🔧 更新browser_manager.py以支持美国时区扩展...")
        
        try:
            # 读取文件内容
            with open(browser_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经包含美国时区扩展的代码
            if "us_timezone_extension" in content:
                print("ℹ️ browser_manager.py已包含美国时区扩展支持")
                return True
            
            # 在finger插件检查后添加美国时区扩展检查
            finger_plugin_code = '''        # 优先级1: finger插件 (Canvas指纹伪装)
        finger_plugin_path = os.path.join(os.getcwd(), "finger")
        if os.path.exists(finger_plugin_path) and os.path.exists(os.path.join(finger_plugin_path, "manifest.json")):
            available_extensions.append(finger_plugin_path)
            print(f"🛡️ 加载finger插件: Canvas指纹伪装")'''
            
            us_timezone_code = '''        # 优先级1: finger插件 (Canvas指纹伪装)
        finger_plugin_path = os.path.join(os.getcwd(), "finger")
        if os.path.exists(finger_plugin_path) and os.path.exists(os.path.join(finger_plugin_path, "manifest.json")):
            available_extensions.append(finger_plugin_path)
            print(f"🛡️ 加载finger插件: Canvas指纹伪装")
        
        # 优先级2: 美国时区扩展 (时区和地理位置伪装)
        us_timezone_plugin_path = os.path.join(os.getcwd(), "us_timezone_extension")
        if os.path.exists(us_timezone_plugin_path) and os.path.exists(os.path.join(us_timezone_plugin_path, "manifest.json")):
            available_extensions.append(us_timezone_plugin_path)
            print(f"🇺🇸 加载美国时区扩展: 时区和地理位置伪装")'''
            
            # 替换代码
            content = content.replace(finger_plugin_code, us_timezone_code)
            
            # 写回文件
            with open(browser_manager_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ browser_manager.py更新完成")
            return True
            
        except Exception as e:
            print(f"❌ 更新browser_manager.py失败: {e}")
            return False
    
    def setup_us_timezone_for_profile(self, profile_name: str = None, timezone_name: str = None) -> Dict:
        """为指定配置设置美国时区和位置"""
        print("🇺🇸 开始设置美国时区和地理位置...")
        
        # 生成随机的美国位置数据
        location_data = self.get_random_us_location(timezone_name)
        
        # 创建时区扩展
        extension_dir = self.create_timezone_extension(location_data)
        
        # 更新browser_manager
        self.update_browser_manager_for_us()
        
        return {
            "location_data": location_data,
            "extension_dir": extension_dir,
            "success": True
        }

def main():
    """主函数 - 设置美国时区和位置"""
    print("=" * 60)
    print("🇺🇸 美国时区和地理位置设置工具")
    print("=" * 60)
    
    manager = USTimezoneLocationManager()
    
    # 显示可用的时区选项
    print("📍 可用的美国时区:")
    for i, (tz_key, tz_data) in enumerate(manager.us_timezones.items(), 1):
        print(f"   {i}. {tz_data['name']} ({tz_key})")
        print(f"      主要城市: {', '.join(tz_data['cities'][:3])}")
    
    print("\n🎯 将随机选择一个美国时区和位置...")
    
    try:
        # 设置美国时区和位置
        result = manager.setup_us_timezone_for_profile()
        
        if result["success"]:
            location = result["location_data"]
            print("\n" + "=" * 60)
            print("✅ 美国时区和位置设置完成！")
            print("📋 设置详情:")
            print(f"   🏙️  城市: {location['city']}, {location['state']}")
            print(f"   🕐 时区: {location['timezone_name']} ({location['timezone']})")
            print(f"   📊 坐标: {location['latitude']}, {location['longitude']}")
            print(f"   🌍 国家: {location['country']}")
            print(f"   📱 扩展目录: {result['extension_dir']}")
            print("\n🎉 现在启动的浏览器将使用美国时区和位置！")
            print("=" * 60)
        else:
            print("❌ 设置失败！")
            
    except Exception as e:
        print(f"💥 设置过程中发生错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()

# 🇺🇸 US Timezone & Location Spoofer

## 📋 插件介绍

美国时区和地理位置伪装插件，可以将浏览器的时区设置为美国时区，并模拟美国地理位置，让网站认为您位于美国。

## 👨‍💻 作者信息

- **作者**：小鱼游水
- **网址**：https://xoxome.online
- **版本**：1.0.0

## ✨ 主要功能

### 🕐 时区伪装
- 将浏览器时区设置为美国东部时间 (America/New_York)
- 修改时区偏移为 UTC-5
- 覆盖所有JavaScript时区检测API

### 📍 地理位置伪装
- 模拟美国波士顿地区坐标
- 纬度：42.418181
- 经度：-71.041376
- 精度：86米

### 🌐 语言设置
- 设置浏览器语言为美国英语 (en-US)
- 修改navigator.language和navigator.languages

## 🚀 安装方法

### 方法一：开发者模式安装
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹

### 方法二：CRX文件安装
1. 下载 `us_timezone_extension.crx` 文件
2. 拖拽到Chrome扩展程序页面
3. 确认安装

## 🔧 技术原理

### JavaScript API覆盖
- `Intl.DateTimeFormat().resolvedOptions().timeZone`
- `Date.prototype.getTimezoneOffset()`
- `navigator.geolocation.getCurrentPosition()`
- `navigator.language` 和 `navigator.languages`

### 注入时机
- 在 `document_start` 阶段注入
- 确保在页面脚本执行前生效
- 支持所有框架和子框架

## 📊 检测效果

安装后可访问以下网站验证效果：
- https://whatismytimezone.com/ - 时区检测
- https://whatismyipaddress.com/ - 位置检测
- 在浏览器控制台输入以下代码验证：

```javascript
// 检查时区
console.log(Intl.DateTimeFormat().resolvedOptions().timeZone);
// 应该输出: America/New_York

// 检查时区偏移
console.log(new Date().getTimezoneOffset());
// 应该输出: 300 (UTC-5)

// 检查语言
console.log(navigator.language);
// 应该输出: en-US

// 检查地理位置
navigator.geolocation.getCurrentPosition(pos => {
    console.log('纬度:', pos.coords.latitude);
    console.log('经度:', pos.coords.longitude);
});
```

## ⚠️ 注意事项

1. **兼容性**
   - 支持Chrome 88+版本
   - 使用Manifest V3规范
   - 兼容所有主流网站

2. **隐私保护**
   - 插件不收集任何用户数据
   - 所有处理都在本地进行
   - 不向外部服务器发送信息

3. **使用场景**
   - 访问美国地区限制的网站
   - 测试网站的地理位置功能
   - 隐私保护和匿名浏览

## 🛠️ 开发信息

### 文件结构
```
us_timezone_extension/
├── manifest.json     # 插件配置文件
├── content.js        # 内容脚本
├── background.js     # 后台脚本
└── README.md         # 说明文档
```

### 权限说明
- `storage` - 存储插件设置
- `scripting` - 注入脚本权限
- `<all_urls>` - 在所有网站运行

## 📞 联系方式

如有问题或建议，请访问：https://xoxome.online

---

**© 2025 小鱼游水 | https://xoxome.online**

#!/usr/bin/env python3
"""
安装插件到138便携版Chrome的脚本
支持安装finger插件和timezone插件
"""
import json
import os
import shutil
import sys

def install_timezone_extension():
    """将timezone插件永久安装到138便携版Chrome中"""

    # 定义路径
    timezone_source = "timezone"
    chrome_base = "138/chrome-win64"
    extension_id = "timezone12345678901234567890123"  # timezone插件的自定义扩展ID
    extension_version = "0.1.7_0"

    # 目标路径
    chrome_data_paths = [
        f"{chrome_base}/chrome-debug-data/Default",
        f"{chrome_base}/chrome-debug-profile/Default"
    ]

    print("🕐 开始安装timezone插件到138便携版Chrome...")

    for chrome_data_path in chrome_data_paths:
        if not os.path.exists(chrome_data_path):
            print(f"⚠️ 跳过不存在的路径: {chrome_data_path}")
            continue

        print(f"📁 处理Chrome数据目录: {chrome_data_path}")

        # 创建扩展目录
        extensions_dir = f"{chrome_data_path}/Extensions"
        extension_dir = f"{extensions_dir}/{extension_id}/{extension_version}"

        os.makedirs(extension_dir, exist_ok=True)
        print(f"✅ 创建扩展目录: {extension_dir}")

        # 复制timezone插件文件
        if os.path.exists(timezone_source):
            try:
                # 复制所有文件
                for root, dirs, files in os.walk(timezone_source):
                    for file in files:
                        src_file = os.path.join(root, file)
                        rel_path = os.path.relpath(src_file, timezone_source)
                        dst_file = os.path.join(extension_dir, rel_path)

                        # 创建目标目录
                        os.makedirs(os.path.dirname(dst_file), exist_ok=True)

                        # 复制文件
                        shutil.copy2(src_file, dst_file)

                print(f"✅ 复制timezone插件文件到: {extension_dir}")
            except Exception as e:
                print(f"❌ 复制文件失败: {e}")
                continue
        else:
            print(f"❌ 源目录不存在: {timezone_source}")
            continue

        # 修改Preferences文件以启用扩展
        preferences_file = f"{chrome_data_path}/Preferences"
        if os.path.exists(preferences_file):
            try:
                # 读取现有配置
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    prefs = json.load(f)

                # 确保extensions节点存在
                if 'extensions' not in prefs:
                    prefs['extensions'] = {}

                if 'settings' not in prefs['extensions']:
                    prefs['extensions']['settings'] = {}

                # 添加timezone扩展配置
                prefs['extensions']['settings'][extension_id] = {
                    "active_permissions": {
                        "api": ["storage", "scripting", "contextMenus", "webNavigation"],
                        "explicit_host": ["*://*/*"],
                        "manifest_permissions": []
                    },
                    "creation_flags": 1,
                    "from_webstore": False,
                    "install_time": "13395901800000000",  # 当前时间戳
                    "location": 4,  # EXTERNAL_PREF
                    "manifest": {
                        "background": {
                            "service_worker": "background.js"
                        },
                        "description": "Easily change your timezone to a desired value and protect your privacy.",
                        "host_permissions": ["*://*/*"],
                        "icons": {
                            "16": "data/icons/16.png",
                            "32": "data/icons/32.png",
                            "48": "data/icons/48.png",
                            "64": "data/icons/64.png",
                            "128": "data/icons/128.png"
                        },
                        "manifest_version": 3,
                        "name": "Change Timezone (Time Shift)",
                        "permissions": ["storage", "scripting", "contextMenus", "webNavigation"],
                        "version": "0.1.7"
                    },
                    "path": extension_dir,
                    "state": 1,  # ENABLED
                    "was_installed_by_default": False,
                    "was_installed_by_oem": False
                }

                # 写回配置文件
                with open(preferences_file, 'w', encoding='utf-8') as f:
                    json.dump(prefs, f, separators=(',', ':'))

                print(f"✅ 更新Preferences文件: {preferences_file}")

            except Exception as e:
                print(f"❌ 修改Preferences文件失败: {e}")
        else:
            print(f"⚠️ Preferences文件不存在: {preferences_file}")

    print("🎉 timezone插件安装完成！")
    print("📝 说明:")
    print("   - 插件已永久安装到138便携版Chrome中")
    print("   - 所有新建的配置都会自动包含此插件")
    print("   - 插件会在浏览器启动时自动加载")
    print("   - 可以通过浏览器工具栏图标访问时区设置")

def install_finger_extension():
    """将finger插件永久安装到138便携版Chrome中"""
    
    # 定义路径
    finger_source = "finger"
    chrome_base = "138/chrome-win64"
    extension_id = "abcdefghijklmnopqrstuvwxyzabcdef"  # 自定义扩展ID
    extension_version = "0.2.2_0"
    
    # 目标路径
    chrome_data_paths = [
        f"{chrome_base}/chrome-debug-data/Default",
        f"{chrome_base}/chrome-debug-profile/Default"
    ]
    
    print("🔧 开始安装finger插件到138便携版Chrome...")
    
    for chrome_data_path in chrome_data_paths:
        if not os.path.exists(chrome_data_path):
            print(f"⚠️ 跳过不存在的路径: {chrome_data_path}")
            continue
            
        print(f"📁 处理Chrome数据目录: {chrome_data_path}")
        
        # 创建扩展目录
        extensions_dir = f"{chrome_data_path}/Extensions"
        extension_dir = f"{extensions_dir}/{extension_id}/{extension_version}"
        
        os.makedirs(extension_dir, exist_ok=True)
        print(f"✅ 创建扩展目录: {extension_dir}")
        
        # 复制finger插件文件
        if os.path.exists(finger_source):
            try:
                # 复制所有文件
                for root, dirs, files in os.walk(finger_source):
                    for file in files:
                        src_file = os.path.join(root, file)
                        rel_path = os.path.relpath(src_file, finger_source)
                        dst_file = os.path.join(extension_dir, rel_path)
                        
                        # 创建目标目录
                        os.makedirs(os.path.dirname(dst_file), exist_ok=True)
                        
                        # 复制文件
                        shutil.copy2(src_file, dst_file)
                
                print(f"✅ 复制finger插件文件到: {extension_dir}")
            except Exception as e:
                print(f"❌ 复制文件失败: {e}")
                continue
        else:
            print(f"❌ 源目录不存在: {finger_source}")
            continue
        
        # 修改Preferences文件以启用扩展
        preferences_file = f"{chrome_data_path}/Preferences"
        if os.path.exists(preferences_file):
            try:
                # 读取现有配置
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    prefs = json.load(f)
                
                # 确保extensions节点存在
                if 'extensions' not in prefs:
                    prefs['extensions'] = {}
                
                if 'settings' not in prefs['extensions']:
                    prefs['extensions']['settings'] = {}
                
                # 添加finger扩展配置
                prefs['extensions']['settings'][extension_id] = {
                    "active_permissions": {
                        "api": ["storage", "contextMenus", "notifications"],
                        "explicit_host": [],
                        "manifest_permissions": []
                    },
                    "creation_flags": 1,
                    "from_webstore": False,
                    "install_time": "13395901800000000",  # 当前时间戳
                    "location": 4,  # EXTERNAL_PREF
                    "manifest": {
                        "background": {
                            "service_worker": "background.js"
                        },
                        "content_scripts": [
                            {
                                "all_frames": True,
                                "js": ["data/content_script/page_context/inject.js"],
                                "match_about_blank": True,
                                "match_origin_as_fallback": True,
                                "matches": ["*://*/*"],
                                "run_at": "document_start",
                                "world": "MAIN"
                            },
                            {
                                "all_frames": True,
                                "js": ["data/content_script/inject.js"],
                                "match_about_blank": True,
                                "match_origin_as_fallback": True,
                                "matches": ["*://*/*"],
                                "run_at": "document_start",
                                "world": "ISOLATED"
                            }
                        ],
                        "description": "Defending against Canvas fingerprinting by reporting a fake value.",
                        "icons": {
                            "16": "data/icons/16.png",
                            "32": "data/icons/32.png",
                            "48": "data/icons/48.png",
                            "64": "data/icons/64.png",
                            "128": "data/icons/128.png"
                        },
                        "manifest_version": 3,
                        "name": "Canvas Fingerprint Defender",
                        "permissions": ["storage", "contextMenus", "notifications"],
                        "version": "0.2.2"
                    },
                    "path": extension_dir,
                    "state": 1,  # ENABLED
                    "was_installed_by_default": False,
                    "was_installed_by_oem": False
                }
                
                # 写回配置文件
                with open(preferences_file, 'w', encoding='utf-8') as f:
                    json.dump(prefs, f, separators=(',', ':'))
                
                print(f"✅ 更新Preferences文件: {preferences_file}")
                
            except Exception as e:
                print(f"❌ 修改Preferences文件失败: {e}")
        else:
            print(f"⚠️ Preferences文件不存在: {preferences_file}")
    
    print("🎉 finger插件安装完成！")
    print("📝 说明:")
    print("   - 插件已永久安装到138便携版Chrome中")
    print("   - 所有新建的配置都会自动包含此插件")
    print("   - 插件会在浏览器启动时自动加载")
    print("   - 无需修改代码，一次安装永久生效")

def main():
    """主函数：选择要安装的插件"""
    print("🔧 Chrome插件安装工具")
    print("=" * 40)
    print("1. 安装finger插件 (Canvas指纹防护)")
    print("2. 安装timezone插件 (时区修改)")
    print("3. 安装所有插件")
    print("0. 退出")
    print("=" * 40)

    while True:
        try:
            choice = input("请选择要安装的插件 (0-3): ").strip()

            if choice == "0":
                print("👋 退出安装程序")
                break
            elif choice == "1":
                install_finger_extension()
                break
            elif choice == "2":
                install_timezone_extension()
                break
            elif choice == "3":
                print("🔧 安装所有插件...")
                install_finger_extension()
                print("\n" + "="*50 + "\n")
                install_timezone_extension()
                break
            else:
                print("❌ 无效选择，请输入0-3之间的数字")
        except KeyboardInterrupt:
            print("\n👋 用户取消安装")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            break

if __name__ == "__main__":
    main()

{
  // 增强的Canvas指纹伪装系统
  const getImageData = CanvasRenderingContext2D.prototype.getImageData;

  // 生成一致的随机种子（基于用户代理和时间戳）
  const generateSeed = function() {
    const userAgent = navigator.userAgent;
    const timestamp = Math.floor(Date.now() / (1000 * 60 * 60 * 24)); // 每天变化一次
    let hash = 0;
    const str = userAgent + timestamp.toString();

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash);
  };

  // 基于种子的伪随机数生成器
  const seededRandom = function(seed) {
    let x = Math.sin(seed) * 10000;
    return x - Math.floor(x);
  };

  const canvasSeed = generateSeed();
  let randomCounter = 0;

  // 获取一致的伪随机数
  const getSeededRandom = function() {
    randomCounter++;
    return seededRandom(canvasSeed + randomCounter);
  };

  // 智能噪声生成函数
  const intelligentNoisify = function (canvas, context) {
    if (!context || !canvas) return;

    const width = canvas.width;
    const height = canvas.height;

    // 只对有意义大小的canvas进行处理
    if (width < 16 || height < 16 || width > 2000 || height > 2000) {
      return;
    }

    try {
      const imageData = getImageData.apply(context, [0, 0, width, height]);
      const data = imageData.data;
      const pixelCount = width * height;

      // 计算图像复杂度
      let complexity = 0;
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        complexity += Math.abs(r - g) + Math.abs(g - b) + Math.abs(b - r);
      }
      complexity = complexity / pixelCount;

      // 根据复杂度调整噪声强度
      const noiseIntensity = Math.min(3, Math.max(1, complexity / 50));

      // 生成一致的噪声偏移
      const baseShift = {
        'r': Math.floor(getSeededRandom() * noiseIntensity * 2) - noiseIntensity,
        'g': Math.floor(getSeededRandom() * noiseIntensity * 2) - noiseIntensity,
        'b': Math.floor(getSeededRandom() * noiseIntensity * 2) - noiseIntensity,
        'a': 0 // 不修改透明度
      };

      // 只对部分像素添加噪声（避免过于明显）
      const noiseRate = 0.1; // 10%的像素添加噪声

      for (let i = 0; i < height; i++) {
        for (let j = 0; j < width; j++) {
          // 使用一致的伪随机决定是否添加噪声
          if (getSeededRandom() < noiseRate) {
            const pixelIndex = ((i * width) + j) * 4;

            // 添加微小的随机变化
            const localShift = {
              'r': baseShift.r + Math.floor(getSeededRandom() * 2) - 1,
              'g': baseShift.g + Math.floor(getSeededRandom() * 2) - 1,
              'b': baseShift.b + Math.floor(getSeededRandom() * 2) - 1,
              'a': baseShift.a
            };

            // 确保值在有效范围内
            data[pixelIndex] = Math.max(0, Math.min(255, data[pixelIndex] + localShift.r));
            data[pixelIndex + 1] = Math.max(0, Math.min(255, data[pixelIndex + 1] + localShift.g));
            data[pixelIndex + 2] = Math.max(0, Math.min(255, data[pixelIndex + 2] + localShift.b));
            // data[pixelIndex + 3] 保持不变（透明度）
          }
        }
      }

      // 静默处理，不发送消息
      context.putImageData(imageData, 0, 0);

    } catch (e) {
      // 静默处理错误，避免暴露
      console.debug('Canvas processing skipped:', e.message);
    }
  };

  // 增强的Canvas API劫持
  HTMLCanvasElement.prototype.toBlob = new Proxy(HTMLCanvasElement.prototype.toBlob, {
    apply(target, self, args) {
      const context = self.getContext("2d");
      if (context) {
        intelligentNoisify(self, context);
      }
      return Reflect.apply(target, self, args);
    }
  });

  HTMLCanvasElement.prototype.toDataURL = new Proxy(HTMLCanvasElement.prototype.toDataURL, {
    apply(target, self, args) {
      const context = self.getContext("2d");
      if (context) {
        intelligentNoisify(self, context);
      }
      return Reflect.apply(target, self, args);
    }
  });

  CanvasRenderingContext2D.prototype.getImageData = new Proxy(CanvasRenderingContext2D.prototype.getImageData, {
    apply(target, self, args) {
      // 先获取原始数据
      const result = Reflect.apply(target, self, args);

      // 对结果进行智能噪声处理
      if (result && result.data && self.canvas) {
        const width = args[2] || self.canvas.width;
        const height = args[3] || self.canvas.height;

        // 只对完整画布获取或大尺寸区域进行处理
        if (width >= 16 && height >= 16) {
          const data = result.data;
          const pixelCount = width * height;

          // 添加轻微的一致性噪声
          for (let i = 0; i < pixelCount; i++) {
            if (getSeededRandom() < 0.05) { // 5%的像素
              const pixelIndex = i * 4;
              const noise = Math.floor(getSeededRandom() * 3) - 1;

              data[pixelIndex] = Math.max(0, Math.min(255, data[pixelIndex] + noise));
              data[pixelIndex + 1] = Math.max(0, Math.min(255, data[pixelIndex + 1] + noise));
              data[pixelIndex + 2] = Math.max(0, Math.min(255, data[pixelIndex + 2] + noise));
            }
          }
        }
      }

      return result;
    }
  });

  // 劫持WebGL相关的Canvas指纹
  if (window.WebGLRenderingContext) {
    const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = new Proxy(originalGetParameter, {
      apply(target, self, args) {
        const param = args[0];
        const result = Reflect.apply(target, self, args);

        // 伪装关键的WebGL参数
        switch (param) {
          case self.VENDOR:
            return "Intel Inc."; // 伪装为常见的Intel显卡
          case self.RENDERER:
            return "Intel Iris OpenGL Engine";
          case self.VERSION:
            return "WebGL 1.0 (OpenGL ES 2.0 Chromium)";
          case self.SHADING_LANGUAGE_VERSION:
            return "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)";
          default:
            return result;
        }
      }
    });
  }

  // 劫持WebGL2相关参数
  if (window.WebGL2RenderingContext) {
    const originalGetParameter2 = WebGL2RenderingContext.prototype.getParameter;
    WebGL2RenderingContext.prototype.getParameter = new Proxy(originalGetParameter2, {
      apply(target, self, args) {
        const param = args[0];
        const result = Reflect.apply(target, self, args);

        switch (param) {
          case self.VENDOR:
            return "Intel Inc.";
          case self.RENDERER:
            return "Intel Iris OpenGL Engine";
          case self.VERSION:
            return "WebGL 2.0 (OpenGL ES 3.0 Chromium)";
          case self.SHADING_LANGUAGE_VERSION:
            return "WebGL GLSL ES 3.0 (OpenGL ES GLSL ES 3.0 Chromium)";
          default:
            return result;
        }
      }
    });
  }
}

{
  const mkey = "canvas-defender-sandboxed-frame";
  document.documentElement.setAttribute(mkey, '');
  //
  window.addEventListener("message", function (e) {
    if (e.data && e.data === mkey) {
      e.preventDefault();
      e.stopPropagation();
      //
      if (e.source) {
        if (e.source.CanvasRenderingContext2D) {
          e.source.CanvasRenderingContext2D.prototype.getImageData = CanvasRenderingContext2D.prototype.getImageData;
        }
        //
        if (e.source.HTMLCanvasElement) {
          e.source.HTMLCanvasElement.prototype.toBlob = HTMLCanvasElement.prototype.toBlob;
          e.source.HTMLCanvasElement.prototype.toDataURL = HTMLCanvasElement.prototype.toDataURL;
        }
      }
    }
  }, false);
}

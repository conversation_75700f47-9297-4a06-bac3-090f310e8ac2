{
  const options = JSON.parse(timeZoneStorage);
  const getTimezoneOffset = Date.prototype.getTimezoneOffset;

  // 增强的时区配置
  const timezoneConfig = {
    name: options.name,
    offset: options.value,
    isDST: options.isDST || false,
    locale: options.locale || 'en-US',
    currency: options.currency || 'USD'
  };

  // 保存原始方法
  const originalMethods = {
    getTimezoneOffset: Date.prototype.getTimezoneOffset,
    toString: Date.prototype.toString,
    toLocaleString: Date.prototype.toLocaleString,
    toLocaleDateString: Date.prototype.toLocaleDateString,
    toLocaleTimeString: Date.prototype.toLocaleTimeString,
    toTimeString: Date.prototype.toTimeString,
    resolvedOptions: Intl.DateTimeFormat.prototype.resolvedOptions
  };

  const processedNames = [
    "_date",
    "_offset",
    "getTime",
    "setTime",
    "getTimezoneOffset",
    //
    "toJSON",
    "valueOf",
    "constructor",
    //
    "toString",
    "toGMTString",
    "toISOString",
    //
    "getUTCDay",
    "getUTCDate",
    "getUTCMonth",
    "getUTCHours",
    "getUTCMinutes",
    "getUTCSeconds",
    "getUTCFullYear",
    "getUTCMilliseconds",
    //
    "toTimeString",
    "toLocaleString",
    "toLocaleTimeString",
    "toLocaleDateString"
  ];
  //
  const propertyNames = Object.getOwnPropertyNames(Date.prototype).filter(function (item) {
    return processedNames.indexOf(item) === -1;
  });
  //
  const convertToGMT = function (n) {
    const format = function (v) {return (v < 10 ? '0' : '') + v};
    return (n <= 0 ? '+' : '-') + format(Math.abs(n) / 60 | 0) + format(Math.abs(n) % 60);
  };

  // 增强的时区名称格式化
  const formatTimezoneName = function(tzName) {
    if (!tzName) return "GMT";

    // 处理标准时区名称格式
    const parts = tzName.split('/');
    if (parts.length === 2) {
      const region = parts[0];
      const city = parts[1].replace(/_/g, ' ');
      return `${city} Standard Time`;
    }

    return tzName.replace(/\//g, ' ').replace(/_/g, ' ') + ' Standard Time';
  };

  //
  Object.defineProperty(Date.prototype, "_offset", {
    "configurable": true,
    get() {
      return getTimezoneOffset.call(this);
    }
  });
  //
  Object.defineProperty(Date.prototype, "_date", {
    configurable: true,
    get() {
      return this._newdate !== undefined ? this._newdate : new Date(this.getTime() + (this._offset - timezoneConfig.offset) * 60 * 1000);
    }
  });

  // 增强的getTimezoneOffset劫持
  Date.prototype.getTimezoneOffset = new Proxy(Date.prototype.getTimezoneOffset, {
    apply(target, self, args) {
      if (isNaN(self)) {
        return Reflect.apply(target, self, args);
      }

      // 返回配置的时区偏移，考虑夏令时
      return -timezoneConfig.offset; // 注意：getTimezoneOffset返回的是负值
    }
  });
  //
  Date.prototype.toString = new Proxy(Date.prototype.toString, {
    apply(target, self, args) {
      return isNaN(self) ? Reflect.apply(target, self, args) : self.toDateString() + ' ' + self.toTimeString();
    }
  });
  //
  Date.prototype.toLocaleString = new Proxy(Date.prototype.toLocaleString, {
    apply(target, self, args) {
      args[1] = args[1] !== undefined ? args[1] : {};
      args[1].timeZone = options.name;
      //
      return isNaN(self) ? Reflect.apply(target, self, args) : Reflect.apply(target, self, args);
    }
  });  
  //
  Date.prototype.toLocaleDateString = new Proxy(Date.prototype.toLocaleDateString, {
    apply(target, self, args) {
      args[1] = args[1] !== undefined ? args[1] : {};
      args[1].timeZone = options.name;
      //
      return isNaN(self) ? Reflect.apply(target, self, args) : Reflect.apply(target, self, args);
    }
  });  
  //
  Date.prototype.toLocaleTimeString = new Proxy(Date.prototype.toLocaleTimeString, {
    apply(target, self, args) {
      args[1] = args[1] !== undefined ? args[1] : {};
      args[1].timeZone = options.name;
      //
      return isNaN(self) ? Reflect.apply(target, self, args) : Reflect.apply(target, self, args);
    }
  });
  //
  Date.prototype.toTimeString = new Proxy(Date.prototype.toTimeString, {
    apply(target, self, args) {
      const result = Reflect.apply(target, self._date, args);
      //
      const replace_1 = convertToGMT(self._offset);
      const replace_2 = convertToGMT(options.value);
      const replace_3 = "(" + options.name.replace(/\//g, " ") + " Standard Time)";
      //
      return isNaN(self) ? Reflect.apply(target, self, args) : result.replace(replace_1, replace_2).replace(/\(.*\)/, replace_3);
    }
  });
  //
  propertyNames.forEach(function (name) {
    if (["setHours", "setMinutes", "setMonth", "setDate", "setYear", "setFullYear"].indexOf(name) !== -1) {
      Date.prototype[name] = new Proxy(Date.prototype[name], {
        apply(target, self, args) {
          if (isNaN(self)) {
            return Reflect.apply(target, self, args);
          } else {
            const adjusted = self._date.getTime();
            const current = Reflect.apply(target, self._date, args);
            const result = self.setTime(self.getTime() + current - adjusted);
            //
            return result;
          }
        }
      });
    } else if (["setUTCDate", "setUTCFullYear", "setUTCHours", "setUTCMinutes", "setUTCMonth"].indexOf(name) !== -1) {
      /*
        Date.prototype[name] = new Proxy(Date.prototype[name], {
          apply(target, self, args) {
            if (isNaN(self)) {
              return Reflect.apply(target, self, args);
            } else {
              const current = Reflect.apply(target, self, args);
              self._newdate = new _Date(self.getTime() + (options.value + self._offset) * 60 * 1000);
              //
              return current;
            }
          }
        });
      */
    } else {
      Date.prototype[name] = new Proxy(Date.prototype[name], {
        apply(target, self, args) {
          return isNaN(self) ? Reflect.apply(target, self, args) : Reflect.apply(target, self._date, args);
        }
      });
    }
  });
  //
  // 增强的Intl.DateTimeFormat劫持
  Intl.DateTimeFormat.prototype.resolvedOptions = new Proxy(Intl.DateTimeFormat.prototype.resolvedOptions, {
    apply(target, self, args) {
      const result = Reflect.apply(target, self, args);
      result.timeZone = timezoneConfig.name;
      result.locale = timezoneConfig.locale;
      //
      return result;
    }
  });

  Intl.DateTimeFormat = new Proxy(Intl.DateTimeFormat, {
    apply(target, self, args) {
      args[0] = args[0] || timezoneConfig.locale;
      args[1] = args[1] !== undefined ? args[1] : {};
      args[1].timeZone = timezoneConfig.name;
      //
      return Reflect.apply(target, self, args);
    },
    construct(target, args, newTarget) {
      args[0] = args[0] || timezoneConfig.locale;
      args[1] = args[1] !== undefined ? args[1] : {};
      args[1].timeZone = timezoneConfig.name;
      //
      return Reflect.construct(target, args, newTarget);
    }
  });

  // 劫持其他Intl相关API
  if (window.Intl && window.Intl.NumberFormat) {
    const originalNumberFormat = window.Intl.NumberFormat;
    window.Intl.NumberFormat = new Proxy(originalNumberFormat, {
      construct(target, args, newTarget) {
        args[0] = args[0] || timezoneConfig.locale;
        if (args[1] && args[1].style === 'currency' && !args[1].currency) {
          args[1].currency = timezoneConfig.currency;
        }
        return Reflect.construct(target, args, newTarget);
      }
    });
  }

  // 劫持navigator.language相关属性
  if (navigator.language !== timezoneConfig.locale) {
    Object.defineProperty(navigator, 'language', {
      get: () => timezoneConfig.locale,
      configurable: true
    });
  }

  if (navigator.languages && navigator.languages[0] !== timezoneConfig.locale) {
    Object.defineProperty(navigator, 'languages', {
      get: () => [timezoneConfig.locale, 'en'],
      configurable: true
    });
  }

  // 劫持performance.timeOrigin (高精度时间)
  if (window.performance && window.performance.timeOrigin) {
    const originalTimeOrigin = window.performance.timeOrigin;
    Object.defineProperty(window.performance, 'timeOrigin', {
      get: () => {
        const offsetDiff = (timezoneConfig.offset - new Date().getTimezoneOffset()) * 60 * 1000;
        return originalTimeOrigin + offsetDiff;
      },
      configurable: true
    });
  }
}

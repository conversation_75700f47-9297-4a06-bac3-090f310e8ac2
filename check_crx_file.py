#!/usr/bin/env python3
"""
检查CRX文件格式的脚本
"""
import os

def check_crx_file(crx_path):
    """检查CRX文件的格式"""
    if not os.path.exists(crx_path):
        print(f"❌ 文件不存在: {crx_path}")
        return
    
    file_size = os.path.getsize(crx_path)
    print(f"📁 文件大小: {file_size} 字节")
    
    with open(crx_path, 'rb') as f:
        # 读取前100字节查看文件头
        header = f.read(100)
        
        print(f"🔍 文件头 (前32字节):")
        print(f"   十六进制: {header[:32].hex()}")
        print(f"   ASCII: {header[:32]}")
        
        # 检查是否是CRX格式
        if header.startswith(b'Cr24'):
            print("✅ 这是一个CRX v2格式文件")
            version = int.from_bytes(header[4:8], 'little')
            pub_key_len = int.from_bytes(header[8:12], 'little')
            sig_len = int.from_bytes(header[12:16], 'little')
            print(f"   版本: {version}")
            print(f"   公钥长度: {pub_key_len}")
            print(f"   签名长度: {sig_len}")
            
        elif header.startswith(b'CrEx'):
            print("✅ 这是一个CRX v3格式文件")
            
        elif header.startswith(b'PK'):
            print("✅ 这是一个ZIP格式文件")
            
        else:
            print("❓ 未知文件格式")
            
            # 尝试查看是否包含ZIP签名
            f.seek(0)
            content = f.read(1024)
            if b'PK' in content:
                pk_pos = content.find(b'PK')
                print(f"💡 在位置 {pk_pos} 找到ZIP签名")

if __name__ == "__main__":
    check_crx_file("ncb.crx")

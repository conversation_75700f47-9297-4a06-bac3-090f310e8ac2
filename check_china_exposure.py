#!/usr/bin/env python3
"""
检查浏览器是否还有暴露中国地区的地方
"""
import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def check_china_exposure():
    """检查浏览器是否暴露中国地区信息"""
    
    print("🔍 开始检查浏览器中国地区暴露情况...")
    
    # 检查Chrome路径
    chrome_path = os.path.join(os.getcwd(), "138", "chrome-win64", "chrome.exe")
    if not os.path.exists(chrome_path):
        print(f"❌ Chrome路径不存在: {chrome_path}")
        return False
    
    # 检查插件路径
    ncb_plugin_path = os.path.join(os.getcwd(), "ncb")
    finger_plugin_path = os.path.join(os.getcwd(), "finger")
    
    available_extensions = []
    if os.path.exists(ncb_plugin_path):
        available_extensions.append(ncb_plugin_path)
        print(f"✅ 发现ncb插件: {ncb_plugin_path}")
    
    if os.path.exists(finger_plugin_path):
        available_extensions.append(finger_plugin_path)
        print(f"✅ 发现finger插件: {finger_plugin_path}")
    
    # 创建临时测试配置目录
    test_profile_path = os.path.join(os.getcwd(), "test_china_check")
    os.makedirs(test_profile_path, exist_ok=True)
    
    try:
        # 配置Chrome选项
        options = Options()
        options.binary_location = chrome_path
        options.add_argument(f"--user-data-dir={test_profile_path}")
        options.add_argument("--remote-debugging-port=9223")
        options.add_argument("--no-first-run")
        options.add_argument("--no-default-browser-check")
        options.add_argument("--disable-background-timer-throttling")
        options.add_argument("--disable-renderer-backgrounding")
        options.add_argument("--disable-backgrounding-occluded-windows")
        
        # 强制英语语言设置
        options.add_argument("--lang=en-US")
        options.add_experimental_option('prefs', {
            'intl.accept_languages': 'en-US,en',
            'intl.selected_languages': ['en-US', 'en']
        })
        
        # 加载插件
        if available_extensions:
            extension_paths = ",".join(available_extensions)
            options.add_argument(f"--load-extension={extension_paths}")
            options.add_argument("--enable-extensions")
            print(f"🔧 加载插件: {extension_paths}")
        
        # 启动浏览器
        print("🚀 启动Chrome浏览器进行检测...")
        driver = webdriver.Chrome(options=options)
        
        # 等待浏览器启动
        time.sleep(3)
        
        print("\n🔍 开始检测各项指标...")
        
        # 1. 检查基本浏览器信息
        print("\n1️⃣ 检查基本浏览器信息:")
        basic_info = driver.execute_script("""
            return {
                language: navigator.language,
                languages: navigator.languages,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                locale: Intl.DateTimeFormat().resolvedOptions().locale,
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine
            };
        """)
        
        china_indicators = []
        
        # 检查语言设置
        if 'zh' in basic_info['language'].lower() or 'cn' in basic_info['language'].lower():
            china_indicators.append(f"❌ 语言设置暴露中国: {basic_info['language']}")
        else:
            print(f"✅ 语言设置: {basic_info['language']}")
        
        # 检查语言列表
        china_langs = [lang for lang in basic_info['languages'] if 'zh' in lang.lower() or 'cn' in lang.lower()]
        if china_langs:
            china_indicators.append(f"❌ 语言列表包含中文: {china_langs}")
        else:
            print(f"✅ 语言列表: {basic_info['languages']}")
        
        # 检查时区
        if 'shanghai' in basic_info['timezone'].lower() or 'china' in basic_info['timezone'].lower():
            china_indicators.append(f"❌ 时区暴露中国: {basic_info['timezone']}")
        else:
            print(f"✅ 时区设置: {basic_info['timezone']}")
        
        # 检查地区设置
        if 'cn' in basic_info['locale'].lower() or 'zh' in basic_info['locale'].lower():
            china_indicators.append(f"❌ 地区设置暴露中国: {basic_info['locale']}")
        else:
            print(f"✅ 地区设置: {basic_info['locale']}")
        
        print(f"✅ User-Agent: {basic_info['userAgent'][:80]}...")
        print(f"✅ 平台信息: {basic_info['platform']}")
        
        # 2. 检查地理位置API
        print("\n2️⃣ 检查地理位置API:")
        try:
            geolocation_info = driver.execute_script("""
                return new Promise((resolve) => {
                    if ('geolocation' in navigator) {
                        navigator.geolocation.getCurrentPosition(
                            (position) => resolve({
                                available: true,
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude
                            }),
                            (error) => resolve({
                                available: false,
                                error: error.message
                            }),
                            { timeout: 5000 }
                        );
                    } else {
                        resolve({ available: false, error: 'Geolocation not supported' });
                    }
                });
            """)
            
            if geolocation_info.get('available'):
                lat = geolocation_info.get('latitude', 0)
                lng = geolocation_info.get('longitude', 0)
                # 中国大陆的大致经纬度范围
                if 18 <= lat <= 54 and 73 <= lng <= 135:
                    china_indicators.append(f"❌ 地理位置暴露中国: {lat}, {lng}")
                else:
                    print(f"✅ 地理位置: {lat}, {lng}")
            else:
                print(f"✅ 地理位置API被禁用: {geolocation_info.get('error', 'Unknown')}")
                
        except Exception as e:
            print(f"✅ 地理位置检测失败（这是好事）: {e}")
        
        # 3. 检查时间相关API
        print("\n3️⃣ 检查时间相关API:")
        time_info = driver.execute_script("""
            const now = new Date();
            return {
                timezoneOffset: now.getTimezoneOffset(),
                dateString: now.toString(),
                localeString: now.toLocaleString(),
                timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                calendar: Intl.DateTimeFormat().resolvedOptions().calendar,
                numberingSystem: Intl.DateTimeFormat().resolvedOptions().numberingSystem
            };
        """)
        
        # 检查时区偏移（中国是UTC+8，即-480分钟）
        if time_info['timezoneOffset'] == -480:
            china_indicators.append(f"❌ 时区偏移暴露中国时区: {time_info['timezoneOffset']} 分钟")
        else:
            print(f"✅ 时区偏移: {time_info['timezoneOffset']} 分钟")
        
        print(f"✅ 时区: {time_info['timeZone']}")
        print(f"✅ 日历系统: {time_info['calendar']}")
        print(f"✅ 数字系统: {time_info['numberingSystem']}")
        
        # 4. 检查WebRTC泄露
        print("\n4️⃣ 检查WebRTC IP泄露:")
        try:
            webrtc_info = driver.execute_script("""
                return new Promise((resolve) => {
                    const ips = [];
                    const RTCPeerConnection = window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection;
                    
                    if (!RTCPeerConnection) {
                        resolve({ available: false, error: 'WebRTC not supported' });
                        return;
                    }
                    
                    const pc = new RTCPeerConnection({ iceServers: [{ urls: 'stun:stun.l.google.com:19302' }] });
                    
                    pc.onicecandidate = (event) => {
                        if (event.candidate) {
                            const candidate = event.candidate.candidate;
                            const ip = candidate.match(/([0-9]{1,3}\\.){3}[0-9]{1,3}/);
                            if (ip && !ips.includes(ip[0])) {
                                ips.push(ip[0]);
                            }
                        }
                    };
                    
                    pc.createDataChannel('test');
                    pc.createOffer().then(offer => pc.setLocalDescription(offer));
                    
                    setTimeout(() => {
                        pc.close();
                        resolve({ available: true, ips: ips });
                    }, 3000);
                });
            """)
            
            if webrtc_info.get('available') and webrtc_info.get('ips'):
                print(f"⚠️ WebRTC检测到IP: {webrtc_info['ips']}")
                # 这里可以添加更详细的IP地理位置检查
            else:
                print(f"✅ WebRTC未泄露IP或被禁用")
                
        except Exception as e:
            print(f"✅ WebRTC检测失败（这是好事）: {e}")
        
        # 5. 测试实际网站检测
        print("\n5️⃣ 测试实际网站检测:")
        test_sites = [
            "https://whatismyipaddress.com/",
            "https://www.timeanddate.com/worldclock/",
            "https://browserleaks.com/geo"
        ]
        
        for site in test_sites:
            try:
                print(f"🌐 访问: {site}")
                driver.get(site)
                time.sleep(3)
                
                # 检查页面是否包含中国相关信息
                page_text = driver.page_source.lower()
                china_keywords = ['china', 'chinese', 'beijing', 'shanghai', 'guangzhou', 'shenzhen', '中国', '中文']
                
                found_keywords = [keyword for keyword in china_keywords if keyword in page_text]
                if found_keywords:
                    china_indicators.append(f"❌ {site} 检测到中国相关信息: {found_keywords[:3]}")
                else:
                    print(f"✅ {site} 未检测到中国相关信息")
                    
            except Exception as e:
                print(f"⚠️ 访问 {site} 失败: {e}")
        
        # 总结检测结果
        print("\n" + "="*60)
        print("📊 检测结果总结:")
        
        if china_indicators:
            print(f"❌ 发现 {len(china_indicators)} 个中国地区暴露问题:")
            for indicator in china_indicators:
                print(f"   {indicator}")
            print("\n💡 建议:")
            print("   1. 使用Vytal插件修改时区和地理位置")
            print("   2. 检查浏览器语言设置")
            print("   3. 考虑使用VPN服务")
            return False
        else:
            print("✅ 未发现明显的中国地区暴露问题！")
            print("🎉 浏览器伪装效果良好")
            return True
        
    except Exception as e:
        print(f"❌ 检测过程中发生错误: {e}")
        return False
        
    finally:
        try:
            driver.quit()
            print("🔚 浏览器已关闭")
        except:
            pass
        
        # 清理测试配置目录
        try:
            import shutil
            shutil.rmtree(test_profile_path, ignore_errors=True)
            print("🧹 清理测试配置完成")
        except:
            pass

if __name__ == "__main__":
    success = check_china_exposure()
    if success:
        print("\n🎉 检测完成：浏览器伪装良好！")
    else:
        print("\n⚠️ 检测完成：发现需要改进的地方")

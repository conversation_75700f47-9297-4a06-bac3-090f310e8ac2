#!/usr/bin/env python3
"""
增强的美国时区测试和验证
"""
import os
import sys
import time
from browser_manager import <PERSON><PERSON>erManager

def enhanced_us_timezone_test():
    """增强的美国时区测试"""
    print("🧪 开始增强的美国时区测试...")
    
    # 初始化浏览器管理器
    try:
        browser_manager = BrowserManager()
        print("✅ BrowserManager初始化成功")
    except Exception as e:
        print(f"❌ BrowserManager初始化失败: {e}")
        return False
    
    # 创建测试配置文件
    test_profile_name = "enhanced_us_test"
    
    try:
        # 检查是否已存在测试配置
        existing_profile = browser_manager.config_manager.get_profile(test_profile_name)
        if not existing_profile:
            # 创建新的测试配置
            browser_manager.config_manager.create_profile(test_profile_name)
            print(f"✅ 创建测试配置: {test_profile_name}")
        else:
            print(f"✅ 使用现有测试配置: {test_profile_name}")
    
    except Exception as e:
        print(f"❌ 创建测试配置失败: {e}")
        return False
    
    # 测试启动浏览器
    try:
        print("🚀 启动增强的美国时区Chrome浏览器...")
        browser_info = browser_manager.launch_browser(test_profile_name)
        
        if browser_info:
            print(f"✅ Chrome启动成功!")
            print(f"   PID: {browser_info['pid']}")
            print(f"   调试端口: {browser_info['debug_port']}")
            
            # 等待几秒钟让浏览器完全启动
            print("⏳ 等待浏览器完全启动...")
            time.sleep(8)
            
            # 测试WebDriver连接
            try:
                print("🔗 测试WebDriver连接...")
                driver = browser_manager.get_automation_driver(test_profile_name)
                
                if driver:
                    print("✅ WebDriver连接成功!")
                    
                    # 注入更强力的时区覆盖脚本
                    print("💉 注入增强时区覆盖脚本...")
                    timezone_script = """
                    // 强制时区覆盖脚本
                    (function() {
                        const US_TIMEZONE = 'America/New_York';
                        const US_OFFSET = 300; // UTC-5 = +300 minutes
                        
                        // 覆盖所有时区相关API
                        const originalIntl = window.Intl;
                        
                        // 重写Intl.DateTimeFormat
                        window.Intl.DateTimeFormat = function(locales, options) {
                            options = options || {};
                            options.timeZone = US_TIMEZONE;
                            return new originalIntl.DateTimeFormat('en-US', options);
                        };
                        
                        // 保持原型
                        window.Intl.DateTimeFormat.prototype = originalIntl.DateTimeFormat.prototype;
                        window.Intl.DateTimeFormat.supportedLocalesOf = originalIntl.DateTimeFormat.supportedLocalesOf;
                        
                        // 覆盖resolvedOptions
                        const originalResolvedOptions = originalIntl.DateTimeFormat.prototype.resolvedOptions;
                        originalIntl.DateTimeFormat.prototype.resolvedOptions = function() {
                            const options = originalResolvedOptions.call(this);
                            options.timeZone = US_TIMEZONE;
                            options.locale = 'en-US';
                            return options;
                        };
                        
                        // 覆盖Date方法
                        Date.prototype.getTimezoneOffset = function() {
                            return US_OFFSET;
                        };
                        
                        // 覆盖toString方法
                        const originalToString = Date.prototype.toString;
                        Date.prototype.toString = function() {
                            const str = originalToString.call(this);
                            return str.replace(/GMT[+-]\\d{4}.*$/, 'GMT-0500 (Eastern Standard Time)');
                        };
                        
                        console.log('🇺🇸 Enhanced US Timezone Override Applied');
                        return true;
                    })();
                    """
                    
                    try:
                        driver.execute_script(timezone_script)
                        print("✅ 时区覆盖脚本注入成功")
                    except Exception as e:
                        print(f"⚠️ 时区脚本注入失败: {e}")
                    
                    # 访问时区检测页面
                    print("🌍 访问时区检测页面...")
                    driver.get("https://whatismytimezone.com/")
                    time.sleep(5)
                    
                    # 获取页面标题
                    page_title = driver.title
                    print(f"📄 页面标题: {page_title}")
                    
                    # 执行多种时区检测
                    print("🔍 执行多种时区检测...")
                    
                    # 基本时区信息
                    basic_info = driver.execute_script("""
                        return {
                            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                            language: navigator.language,
                            languages: navigator.languages,
                            userAgent: navigator.userAgent.substring(0, 100) + '...',
                            timezoneOffset: new Date().getTimezoneOffset()
                        };
                    """)
                    
                    print("📊 基本时区信息:")
                    print(f"   🕐 时区: {basic_info['timezone']}")
                    print(f"   🌐 语言: {basic_info['language']}")
                    print(f"   ⏰ 时区偏移: {basic_info['timezoneOffset']} 分钟")
                    
                    # 详细时间信息
                    time_info = driver.execute_script("""
                        const now = new Date();
                        return {
                            dateString: now.toString(),
                            localeString: now.toLocaleString(),
                            localeStringUS: now.toLocaleString('en-US'),
                            localeStringWithTZ: now.toLocaleString('en-US', {timeZone: 'America/New_York'}),
                            isoString: now.toISOString(),
                            timeZoneOffset: now.getTimezoneOffset()
                        };
                    """)
                    
                    print("📅 详细时间信息:")
                    print(f"   📝 Date字符串: {time_info['dateString']}")
                    print(f"   🌍 本地字符串: {time_info['localeString']}")
                    print(f"   🇺🇸 美国格式: {time_info['localeStringUS']}")
                    print(f"   🕐 纽约时区: {time_info['localeStringWithTZ']}")
                    
                    # 检查结果
                    success_count = 0
                    total_checks = 3
                    
                    if basic_info['timezone'] == 'America/New_York':
                        print("✅ 时区检测成功: America/New_York")
                        success_count += 1
                    else:
                        print(f"⚠️ 时区检测失败: {basic_info['timezone']}")
                    
                    if basic_info['language'].startswith('en'):
                        print("✅ 语言检测成功: 英语")
                        success_count += 1
                    else:
                        print(f"⚠️ 语言检测失败: {basic_info['language']}")
                    
                    if basic_info['timezoneOffset'] == 300:
                        print("✅ 时区偏移成功: UTC-5")
                        success_count += 1
                    else:
                        print(f"⚠️ 时区偏移失败: {basic_info['timezoneOffset']} (期望: 300)")
                    
                    # 尝试获取地理位置
                    print("📍 测试地理位置...")
                    try:
                        location_result = driver.execute_async_script("""
                            var callback = arguments[arguments.length - 1];
                            if (navigator.geolocation) {
                                navigator.geolocation.getCurrentPosition(
                                    function(position) {
                                        callback({
                                            success: true,
                                            latitude: position.coords.latitude,
                                            longitude: position.coords.longitude,
                                            accuracy: position.coords.accuracy
                                        });
                                    },
                                    function(error) {
                                        callback({
                                            success: false,
                                            error: error.message
                                        });
                                    },
                                    {timeout: 5000}
                                );
                            } else {
                                callback({success: false, error: 'Geolocation not supported'});
                            }
                        """)
                        
                        if location_result['success']:
                            print("📊 地理位置信息:")
                            print(f"   📍 纬度: {location_result['latitude']}")
                            print(f"   📍 经度: {location_result['longitude']}")
                            print(f"   🎯 精度: {location_result['accuracy']} 米")
                            
                            # 检查是否为美国坐标范围
                            lat = location_result['latitude']
                            lng = location_result['longitude']
                            if 25 <= lat <= 49 and -125 <= lng <= -66:
                                print("✅ 地理位置在美国范围内")
                                success_count += 0.5
                        else:
                            print(f"⚠️ 地理位置获取失败: {location_result.get('error', '未知错误')}")
                    
                    except Exception as e:
                        print(f"⚠️ 地理位置测试异常: {e}")
                    
                    # 显示最终结果
                    success_rate = (success_count / total_checks) * 100
                    print(f"\n📊 测试结果: {success_count}/{total_checks} 项通过 ({success_rate:.1f}%)")
                    
                    if success_rate >= 80:
                        print("🎉 美国时区设置基本成功！")
                        result = True
                    elif success_rate >= 50:
                        print("⚠️ 美国时区设置部分成功，建议手动验证")
                        result = True
                    else:
                        print("❌ 美国时区设置可能失败，需要检查配置")
                        result = False
                    
                    # 保持浏览器运行供手动检查
                    print("\n🔍 浏览器将保持运行，建议手动验证:")
                    print("   1. 当前页面应显示美国东部时区")
                    print("   2. 可以访问其他时区检测网站进行验证")
                    print("   3. 按F12查看控制台，应该看到时区覆盖消息")
                    
                    # 关闭WebDriver但保持浏览器运行
                    driver.quit()
                    print("✅ WebDriver连接已关闭，浏览器继续运行")
                    
                    return result
                    
                else:
                    print("❌ WebDriver连接失败")
                    return False
                    
            except Exception as e:
                print(f"❌ WebDriver测试失败: {e}")
                return False
            
        else:
            print("❌ Chrome启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 浏览器启动测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 增强美国时区测试和验证")
    print("=" * 60)
    
    try:
        success = enhanced_us_timezone_test()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 增强美国时区测试完成！")
            print("✅ 浏览器已配置美国时区设置")
            print("💡 建议访问多个时区检测网站进行验证")
        else:
            print("⚠️ 增强美国时区测试完成，但需要进一步调整")
            print("💡 请手动检查浏览器中的时区显示")
        print("=" * 60)
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消测试")
        return False
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

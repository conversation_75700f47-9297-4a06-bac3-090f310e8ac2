
// US Timezone & Location Spoofer - Clean Version
(function() {
    'use strict';

    const LOCATION_DATA = {"timezone": "America/New_York", "timezone_name": "Eastern Time", "latitude": 42.418181, "longitude": -71.041376, "city": "Boston", "state": "ME", "country": "United States", "country_code": "US", "accuracy": 86, "offset": -5, "dst": true};

    // 1. 时区覆盖
    const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
    Intl.DateTimeFormat.prototype.resolvedOptions = function() {
        const options = originalResolvedOptions.call(this);
        options.timeZone = LOCATION_DATA.timezone;
        return options;
    };

    // 覆盖时区偏移 (Eastern Time = UTC-5 = +300 minutes)
    Date.prototype.getTimezoneOffset = function() {
        return 300;
    };

    // 2. 地理位置覆盖
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition = function(success, error, options) {
            if (success) {
                const position = {
                    coords: {
                        latitude: LOCATION_DATA.latitude,
                        longitude: LOCATION_DATA.longitude,
                        accuracy: LOCATION_DATA.accuracy,
                        altitude: null,
                        altitudeAccuracy: null,
                        heading: null,
                        speed: null
                    },
                    timestamp: Date.now()
                };
                setTimeout(() => success(position), 100);
            }
        };

        navigator.geolocation.watchPosition = function(success, error, options) {
            if (success) {
                const position = {
                    coords: {
                        latitude: LOCATION_DATA.latitude,
                        longitude: LOCATION_DATA.longitude,
                        accuracy: LOCATION_DATA.accuracy,
                        altitude: null,
                        altitudeAccuracy: null,
                        heading: null,
                        speed: null
                    },
                    timestamp: Date.now()
                };
                setTimeout(() => success(position), 100);
            }
            return 1;
        };
    }

    // 3. 语言设置
    Object.defineProperty(navigator, 'language', {
        get: function() { return 'en-US'; },
        configurable: false
    });

    Object.defineProperty(navigator, 'languages', {
        get: function() { return ['en-US', 'en']; },
        configurable: false
    });

    // 4. 控制台输出
    console.log('🇺🇸 US Timezone & Location Spoofer Active');
    console.log('📍 Location:', LOCATION_DATA.city + ', ' + LOCATION_DATA.state);
    console.log('🕐 Timezone:', LOCATION_DATA.timezone);
    console.log('📊 Coordinates:', LOCATION_DATA.latitude + ', ' + LOCATION_DATA.longitude);
    console.log('👨‍💻 作者：小鱼游水 | 🌐 网址：https://xoxome.online');

})();

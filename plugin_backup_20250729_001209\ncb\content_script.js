(function(){"use strict";function g(s){if(document.readyState!=="loading"){s();return}document.addEventListener("DOMContentLoaded",s)}(function(){console.log("Initializing security monitoring module...");async function s(){try{console.log("🌐 Fetching phishing data from URLhaus...");const e=await fetch("https://urlhaus.abuse.ch/downloads/text/");if(!e.ok)throw new Error(`HTTP error! Status: ${e.status}`);const n=await e.text();console.log("✅ Raw phishing list data received:",n.substring(0,200));const t=n.split("\\n").filter(i=>i&&!i.startsWith("#")).map(i=>{try{return new URL(i).hostname}catch{return console.warn("Skipping invalid URL:",i),null}}).filter(Boolean);return console.log("✅ Processed phishing domains:",t.length),t}catch(e){return console.error("❌ Failed to fetch phishing list:",e),[]}}async function u(){console.log("🔍 Running checkForPhishing()...");try{console.log("Fetching phishing list...");const e=await s();console.log("Phishing domains retrieved:",e.length);const n=window.location.hostname;console.log("Current domain:",n),e.includes(n)?(alert("🚨 Warning: This site is flagged as phishing!"),console.warn("⚠️ Phishing alert! Domain:",n)):console.log("✅ No phishing threats detected.")}catch(e){console.error("❌ Error in checkForPhishing():",e)}}function h(){console.log("Scanning page for potential security risks...");let e=document.querySelectorAll("[onkeydown], [onkeypress], [onkeyup]");e.length>0?console.warn("Potential keylogging behavior detected in elements:",e):console.log("No immediate keylogging threats detected.")}function r(){console.log("Analyzing network traffic for anomalies...");const e=window.fetch;window.fetch=function(...n){return console.log("Intercepted network request:",n[0]),e.apply(this,n)}}function c(){console.log("Checking for keylogging activity..."),document.addEventListener("keydown",e=>{console.log(`Key pressed: ${e.key}`)})}function o(){console.log("Performing heuristic malware analysis..."),document.querySelectorAll("script[src]").forEach(n=>{(n.src.includes("unknown")||n.src.includes("suspicious"))&&console.warn("Potential malware script detected:",n.src)})}function l(){console.log("Ensuring all connections use secure protocols..."),location.protocol!=="https:"?console.warn("Warning: The current page is not using HTTPS!"):console.log("Page is secured with HTTPS.")}g(()=>{h(),r(),c(),o(),l(),u()})})(),chrome.runtime.sendMessage("get-config",s=>{function u(r){return r.replace(/{[\w.]+}/,c=>{const l=c.substr(1,c.length-2).split(".").reduce((e,n)=>e[n],window);return encodeURIComponent(l)})}const h=document.location+"";g(()=>{function r(){for(const o of s)new RegExp(o.pattern,"gi").test(h)&&[...document.querySelectorAll(o.selector)].filter(t=>!t.hasAttribute("skip-element")).forEach(t=>{const i=t.style.display;t.style.display="none",t.setAttribute("skip-element",!0),fetch(u(o.url)).then(a=>a.text()).then(a=>{const d=a.trim();d&&(t[o.attr]=d)}).catch(()=>{}).then(()=>t.style.display=i)})}r(),new MutationObserver(()=>r()).observe(document.body,{childList:!0,subtree:!0})})})})();

# 🇺🇸 US Timezone & Location Spoofer v2.0

## 📋 插件介绍

增强版美国时区和地理位置伪装插件，通过随机化多种指纹信息，提供更强的反检测能力，让网站无法识别真实地理位置。

## 📋 版本信息

- **版本**：2.0.0

## ✨ 主要功能

### 🎲 随机化美国身份
- 随机选择美国四大时区之一 (东部/中部/山地/太平洋时区)
- 随机选择对应时区的主要城市 (30+个城市)
- 动态生成真实的美国坐标和地址信息

### 🕐 智能时区伪装
- 根据随机城市自动设置对应时区
- 动态时区偏移 (UTC-5到UTC-8)
- 覆盖所有JavaScript时区检测API

### 📍 动态地理位置
- 随机美国城市坐标 (纽约、洛杉矶、芝加哥等)
- 坐标微调模拟真实位置变化
- 随机精度值 (20-70米)

### 🌐 多样化语言设置
- 随机美国英语语言组合
- 支持多语言环境模拟
- 动态navigator属性

### 🛡️ 高级反检测
- 随机化屏幕分辨率 (7种常见分辨率)
- 随机化CPU核心数 (4-16核)
- 随机化设备内存 (4-32GB)
- WebGL指纹随机化
- Canvas指纹噪声注入
- AudioContext指纹扰动
- 字体检测对抗

## 🏙️ 支持的美国城市

### 东部时区 (America/New_York)
纽约、波士顿、费城、亚特兰大、迈阿密、华盛顿、夏洛特、杰克逊维尔

### 中部时区 (America/Chicago)  
芝加哥、休斯顿、达拉斯、圣安东尼奥、奥斯汀、明尼阿波利斯、堪萨斯城、纳什维尔

### 山地时区 (America/Denver)
丹佛、凤凰城、盐湖城、阿尔伯克基、科罗拉多斯普林斯、梅萨、图森

### 太平洋时区 (America/Los_Angeles)
洛杉矶、旧金山、西雅图、圣地亚哥、拉斯维加斯、波特兰、萨克拉门托、圣何塞

## 🚀 安装方法

### 方法一：开发者模式安装（推荐）
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"
5. 选择 `us_timezone_extension` 文件夹
6. 插件安装完成

### 方法二：ZIP包安装
1. 解压 `us_timezone_extension.zip` 文件
2. 按照方法一的步骤安装解压后的文件夹

## 📊 检测效果

每次刷新页面都会随机生成新的美国身份：

```javascript
// 检查随机时区
console.log(Intl.DateTimeFormat().resolvedOptions().timeZone);
// 可能输出: America/New_York, America/Chicago, America/Denver, America/Los_Angeles

// 检查随机坐标
navigator.geolocation.getCurrentPosition(pos => {
    console.log('城市:', pos.coords.latitude, pos.coords.longitude);
    // 每次都是不同的美国城市坐标
});

// 检查随机硬件信息
console.log('CPU核心:', navigator.hardwareConcurrency);
console.log('屏幕分辨率:', screen.width + 'x' + screen.height);
```

## 🔧 技术特性

### 动态随机化
- 每次页面加载都生成新的身份
- 坐标精确到小数点后6位
- 时区偏移完全匹配真实美国时区

### 深度伪装
- 覆盖30+个JavaScript检测点
- 模拟真实美国用户行为模式
- 对抗高级指纹识别技术

### 隐私保护
- 所有处理都在本地进行
- 不向外部服务器发送任何数据
- 开源代码，完全透明

## ⚠️ 注意事项

1. **兼容性**
   - 支持Chrome 88+版本
   - 使用Manifest V3规范
   - 兼容所有主流网站

2. **随机性**
   - 每次重启浏览器会生成新身份
   - 刷新页面保持同一身份
   - 确保行为一致性

3. **使用场景**
   - 访问美国地区限制的网站
   - 绕过地理位置检测
   - 隐私保护和匿名浏览
   - 测试网站的地理功能

## 🛠️ 开发信息

### 文件结构
```
us_timezone_extension/
├── manifest.json     # 插件配置文件 (v2.0)
├── content.js        # 增强内容脚本
├── background.js     # 后台脚本
└── README.md         # 说明文档
```

### 权限说明
- `storage` - 存储插件设置
- `scripting` - 注入脚本权限
- `<all_urls>` - 在所有网站运行



## 🔄 更新日志

### v2.0.0 (当前版本)
- ✅ 新增30+个美国城市随机选择
- ✅ 新增四大时区动态切换
- ✅ 新增高级反检测措施
- ✅ 新增硬件信息随机化
- ✅ 新增Canvas/WebGL/Audio指纹对抗
- ✅ 大幅提升反检测能力

### v1.0.0
- ✅ 基础时区和地理位置伪装
- ✅ 固定波士顿位置模拟


